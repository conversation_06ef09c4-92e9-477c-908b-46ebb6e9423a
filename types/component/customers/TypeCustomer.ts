import { CustomerStage, CustomerStatus, CustomerType, CustomTag } from '@/lib/graphql/types/generated/graphql';

export interface CustomerWithBasicDetails {
    id: string;
    name: string;
    email: string;
    status: CustomerStatus;
    phone: string;
    type: CustomerType;
    stage: CustomerStage;
    website?: string;
    tags?: string[];
    accountManager?: string
    customTags: CustomTag[];
}

export interface CustomersControlsProps {
    searchQuery: string;
    onSearch: (e: React.ChangeEvent<HTMLInputElement>) => void;
    onAddCustomer: () => void;
}