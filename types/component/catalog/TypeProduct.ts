import { ProductStatus, Currency, ChargePolicy, DiscountType, DiscountLevel } from "@/lib/graphql/types/generated/graphql";

export interface ProductDimension {
    key: string;
    value: string;
}

// Extended DocumentInput to handle existing documents with URLs and file uploads
// Updated to align with new GraphQL schema and upload flow
export interface ExtendedDocumentInput {
    // Document identification
    id?: string | null;
    requestId: string;

    // Document metadata
    label?: string | null;
    description?: string;
    tags?: string[];

    // File upload properties
    file?: File | null;
    uploadProgress?: number;
    uploadError?: string;
    isUploading?: boolean;
    uploadSuccess?: boolean;

    // S3 URLs (received from backend)
    signedReadURL?: string;
    signedWriteURL?: string;

    // Upload state management
    presignedUrlGenerated?: boolean;
    presignedUrlError?: string;
}

// Custom tag input interface for form handling
export interface ProductCustomTagInput {
    id?: string | null;
    label: string;
    key: string;
    value: string;
    type: string;
    description?: string;
}

export interface ProductFormData {
    productId?: string;
    name: string;
    productCode: string;
    description: string;
    status: ProductStatus;
    dimensions: ProductDimension[];
    pricing: {
        id?: string;
        chargePolicy: ChargePolicy;
        costPrice: {
            value: number;
            currency: Currency;
        };
        listPrice: {
            value: number;
            currency: Currency;
        };
        sellingPrice: {
            value: number;
            currency: Currency;
        };
        unit: {
            unit: number;
            unitType: string;
        };
        discounts: {
            discountType: DiscountType;
            discountLevel: DiscountLevel;
            discountValue: {
                amount: {
                    value: number;
                    currency: Currency;
                };
                percentage: number;
            };
        }[];
        margin: {
            absoluteAmount: {
                value: number;
                currency: Currency;
            };
            percentage: number;
        };
    };
    // Additional details fields
    documents: ExtendedDocumentInput[];
    customTags: ProductCustomTagInput[];
}

export interface ProductUpsertInput {
    id?: string;
    productCode: string;
    name: string;
    description: string;
    dimensions: ProductDimension[];
    pricing: {
        chargePolicy: ChargePolicy;
        costPrice: {
            value: number;
            currency: Currency;
        };
        listPrice: {
            value: number;
            currency: Currency;
        };
        sellingPrice: {
            value: number;
            currency: Currency;
        };
        productUnit: {
            unit: number;
            unitType: string;
        };
        discounts: {
            discountType: DiscountType;
            discountLevel: DiscountLevel;
            discountValue: {
                amount: {
                    value: number;
                    currency: Currency;
                };
                percentage: number;
            };
        }[];
        margin: {
            absoluteAmount: {
                value: number;
                currency: Currency;
            };
            percentage: number;
        };
    };
} 