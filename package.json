{"name": "frontend-core", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "codegen": "graphql-codegen --config codegen.ts", "codegen:watch": "graphql-codegen --config codegen.ts --watch", "graphql:generate": "npm run codegen", "graphql:watch": "npm run codegen:watch"}, "dependencies": {"@apollo/client": "^3.13.8", "@apollo/client-integration-nextjs": "^0.12.2", "@clerk/nextjs": "^6.21.0", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "graphql": "^16.11.0", "lucide-react": "^0.511.0", "next": "15.3.2", "posthog-js": "^1.258.6", "posthog-node": "^5.6.0", "react": "^19.0.0", "react-contenteditable": "^3.3.7", "react-dom": "^19.0.0", "react-hook-form": "^7.59.0", "react-toastify": "^11.0.5", "sanitize-html": "^2.17.0", "uuid": "^11.1.0", "uuidv4": "^6.2.13"}, "devDependencies": {"@graphql-codegen/cli": "^5.0.7", "@graphql-codegen/client-preset": "^4.8.2", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-operations": "^4.6.1", "@graphql-codegen/typescript-react-apollo": "^4.3.3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/sanitize-html": "^2.16.0", "graphql-tag": "^2.12.6", "prettier": "^3.6.0", "tailwindcss": "^4", "typescript": "^5"}}