import React, { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { CustomerStage } from '@/lib/graphql/types/generated/graphql';
import { InputField, SelectField, icons } from './ComponentFormFields';


const IndividualForm: React.FC = () => {
    const { register } = useFormContext();

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className='col-span-2 flex flex-col gap-4 bg-gray-50 p-4 rounded-lg'>
                <div>
                    <SelectField
                        label="Customer Stage *"
                        name="stage"
                        register={register}
                        icon={<icons.Flag className="h-5 w-5" />}
                        required
                    >
                        <option value="" disabled>Select customer stage</option>
                        <option value={CustomerStage.Prospect}>Prospect</option>
                        <option value={CustomerStage.Lead}>Lead</option>
                        <option value={CustomerStage.ConversionInProgress}>Conversion In Progress</option>
                        <option value={CustomerStage.Converted}>Converted</option>
                        <option value={CustomerStage.ConversionFailed}>Conversion Failed</option>
                        <option value={CustomerStage.Churned}>Churned</option>
                    </SelectField>
                </div>
                <div>
                    <InputField
                        label="Full Name *"
                        name="name"
                        register={register}
                        placeholder="Enter full name"
                        icon={<icons.User className="h-5 w-5" />}
                        required
                    />
                </div>
                <div>
                    <InputField
                        label="Email Address *"
                        name="email"
                        type="email"
                        register={register}
                        placeholder="Enter email address"
                        icon={<icons.Mail className="h-5 w-5" />}
                        required
                    />
                </div>
                <div>
                    <InputField
                        label="Address"
                        name="address"
                        type="text"
                        register={register}
                        placeholder="Enter address"
                        icon={<icons.MapPin className="h-5 w-5" />}
                    />
                </div>
            </div>
            <div>
                <InputField
                    label="Phone Number"
                    name="phone"
                    type="tel"
                    register={register}
                    placeholder="Enter phone number"
                    icon={<icons.Phone className="h-5 w-5" />}
                />
            </div>
            <div>
                <InputField
                    label="Referral Source"
                    name="referralSource"
                    register={register}
                    placeholder="How did they find you?"
                    icon={<icons.Users className="h-5 w-5" />}
                />
            </div>
            <div>
                <InputField
                    label="Website"
                    name="website"
                    register={register}
                    placeholder="https://www.customer.com"
                    icon={<icons.Globe className="h-5 w-5" />}
                />
            </div>
            <div>
                <InputField
                    label="Tags"
                    name="plainTags"
                    register={register}
                    placeholder="Enter tags"
                    icon={<icons.Tag className="h-5 w-5" />}
                />
            </div>
        </div>
    );
};

export default IndividualForm;