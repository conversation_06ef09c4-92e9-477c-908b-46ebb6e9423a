"use client";

import React from "react";
import { History, Clock, Mail } from "lucide-react";

interface EmailHistoryProps {
  customerId: string;
}

const ComponentEmailHistory: React.FC<EmailHistoryProps> = ({ customerId }) => {
  return (
    <div className="p-8">
      {/* Header */}
      <div className="flex items-center gap-3 mb-6">
        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
          <History className="w-5 h-5 text-blue-600" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Email History</h3>
          <p className="text-sm text-gray-600">
            View all email communications with this customer
          </p>
        </div>
      </div>

      {/* Coming Soon Content */}
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Clock className="w-8 h-8 text-gray-400" />
        </div>
        <h4 className="text-xl font-semibold text-gray-900 mb-2">
          Coming Soon
        </h4>
        <p className="text-gray-600 mb-6 max-w-md mx-auto">
          Email history tracking is currently under development. Soon you'll be
          able to view all email communications, track delivery status, and
          manage email threads with your customers.
        </p>

        {/* Feature Preview */}
        <div className="bg-gray-50 rounded-lg p-6 max-w-2xl mx-auto">
          <h5 className="text-sm font-medium text-gray-900 mb-3">
            Upcoming Features:
          </h5>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <Mail className="w-4 h-4 text-secondary" />
              <span>Email thread tracking</span>
            </div>
            <div className="flex items-center gap-2">
              <Mail className="w-4 h-4 text-secondary" />
              <span>Delivery status monitoring</span>
            </div>
            <div className="flex items-center gap-2">
              <Mail className="w-4 h-4 text-secondary" />
              <span>Email templates library</span>
            </div>
            <div className="flex items-center gap-2">
              <Mail className="w-4 h-4 text-secondary" />
              <span>Automated follow-ups</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComponentEmailHistory;
