"use client";

import React, { useState } from "react";
import { Mail, History } from "lucide-react";
import ComponentEmailComposition from "./ComponentEmailComposition";
import ComponentEmailHistory from "./ComponentEmailHistory";

interface CustomerEmailProps {
  customerId: string;
}

const ComponentCustomerEmail: React.FC<CustomerEmailProps> = ({
  customerId,
}) => {
  const [activeTab, setActiveTab] = useState<"compose" | "history">("compose");

  return (
    <div className="space-y-6 mt-20 ">
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Customer Email
        </h2>
        <p className="text-gray-600">
          Send emails and view email history for this customer
        </p>
      </div>
      {/* Tab Navigation */}
      <div className="flex flex-row justify-center gap-2 max-w-fit mx-auto bg-white border border-gray-100 p-4 w-fit rounded-md">
        <div className="flex space-x-1">
          <button
            onClick={() => setActiveTab("compose")}
            className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
              activeTab === "compose"
                ? "bg-secondary text-white shadow-md transform scale-105"
                : "text-gray-600 hover:text-secondary hover:bg-gray-50 hover:shadow-sm hover:mr-2"
            }`}
          >
            <Mail className="h-4 w-4" />
            Compose Email
          </button>
          <button
            onClick={() => setActiveTab("history")}
            className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
              activeTab === "history"
                ? "bg-secondary text-white shadow-md transform scale-105"
                : "text-gray-600 hover:text-secondary hover:bg-gray-50 hover:shadow-sm hover:ml-2"
            }`}
          >
            <History className="h-4 w-4" />
            Email History
          </button>
        </div>
      </div>

      {/* Tab Content */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
        {activeTab === "compose" && (
          <ComponentEmailComposition customerId={customerId} />
        )}
        {activeTab === "history" && (
          <ComponentEmailHistory customerId={customerId} />
        )}
      </div>
    </div>
  );
};

export default ComponentCustomerEmail;
