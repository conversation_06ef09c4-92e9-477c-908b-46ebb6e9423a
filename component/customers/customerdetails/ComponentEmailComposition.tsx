"use client";

import React, { useState, useRef, useEffect } from "react";
import {
  Mail,
  Send,
  User,
  Paperclip,
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
} from "lucide-react";
import {
  useGetCustomerByIdQuery,
  useCustomerSendEmailMutation,
} from "@/lib/graphql/types/generated/hooks";
import { CompanyEmailTemplateType } from "@/lib/graphql/types/generated/graphql";
import { getUserFriendlyErrorMessage } from "@/lib/graphql/utils/errorHandling";
import ComponentLoading from "@/component/common/ComponentLoading";
import ComponentNote from "@/component/common/ComponentNote";
import ContentEditable from "react-contenteditable";
import sanitizeHtml from "sanitize-html";
import { toast } from "react-toastify";

interface EmailCompositionProps {
  customerId: string;
}

interface EmailFormData {
  to: string;
  subject: string;
  cc: string;
  bcc: string;
  body: string;
}

const ComponentEmailComposition: React.FC<EmailCompositionProps> = ({
  customerId,
}) => {
  const {
    data: customerData,
    loading: isLoadingCustomer,
    error: customerError,
  } = useGetCustomerByIdQuery({
    variables: { id: customerId },
  });

  const [customerSendEmail, { loading: isSending, error: sendError }] =
    useCustomerSendEmailMutation();

  const [formData, setFormData] = useState<EmailFormData>({
    to: "",
    subject: "",
    cc: "",
    bcc: "",
    body: "",
  });

  const [isBodyEditing, setIsBodyEditing] = useState(false);
  const contentEditableRef = useRef<any>(null);

  // Auto-populate customer email when data loads
  useEffect(() => {
    if (customerData?.getCustomer) {
      const customer = customerData.getCustomer;
      let customerEmail = "";

      if (customer.__typename === "CustomerBusiness") {
        customerEmail = customer.basicDetails.contactDetails.email;
      } else if (customer.__typename === "CustomerIndividual") {
        customerEmail = customer.basicDetails.contactDetails.email;
      }

      setFormData((prev) => ({
        ...prev,
        to: customerEmail,
      }));
    }
  }, [customerData]);

  const handleInputChange = (field: keyof EmailFormData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleBodyChange = (e: any) => {
    const sanitizedContent = sanitizeHtml(e.target.value, {
      allowedTags: [
        "b",
        "i",
        "u",
        "strong",
        "em",
        "p",
        "br",
        "ul",
        "ol",
        "li",
        "h1",
        "h2",
        "h3",
        "h4",
        "h5",
        "h6",
      ],
      allowedAttributes: {},
    });
    setFormData((prev) => ({
      ...prev,
      body: sanitizedContent,
    }));
  };

  const handleBodyBlur = () => {
    setIsBodyEditing(false);
  };

  const formatText = (command: string) => {
    document.execCommand(command, false);
    if (contentEditableRef.current) {
      const content = contentEditableRef.current.innerHTML;
      const sanitizedContent = sanitizeHtml(content, {
        allowedTags: [
          "b",
          "i",
          "u",
          "strong",
          "em",
          "p",
          "br",
          "ul",
          "ol",
          "li",
          "h1",
          "h2",
          "h3",
          "h4",
          "h5",
          "h6",
        ],
        allowedAttributes: {},
      });
      setFormData((prev) => ({
        ...prev,
        body: sanitizedContent,
      }));
    }
  };

  const handleSendEmail = async () => {
    if (!formData.to || !formData.subject || !formData.body) {
      toast.error("Please fill in all required fields (To, Subject, and Body)");
      return;
    }

    try {
      const recipients = formData.to
        .split(",")
        .map((email) => email.trim())
        .filter((email) => email);
      const ccRecipients = formData.cc
        ? formData.cc
            .split(",")
            .map((email) => email.trim())
            .filter((email) => email)
        : [];
      const bccRecipients = formData.bcc
        ? formData.bcc
            .split(",")
            .map((email) => email.trim())
            .filter((email) => email)
        : [];

      await customerSendEmail({
        variables: {
          input: {
            customerId,
            email: {
              subject: formData.subject,
              body: formData.body,
              recipients,
              cc: ccRecipients,
              bcc: bccRecipients,
              attachments: [],
            },
          },
        },
      });

      toast.success("Email sent successfully!");

      // Reset form except for the 'to' field
      setFormData((prev) => ({
        ...prev,
        subject: "",
        cc: "",
        bcc: "",
        body: "",
      }));
    } catch (error) {
      console.error("Error sending email:", error);
      toast.error("Failed to send email. Please try again.");
    }
  };

  if (isLoadingCustomer) {
    return (
      <div className="p-8">
        <ComponentLoading
          message="Loading customer information..."
          className="min-h-[200px]"
        />
      </div>
    );
  }

  if (customerError) {
    return (
      <div className="p-8">
        <ComponentNote isError={true}>
          {getUserFriendlyErrorMessage(customerError)}
        </ComponentNote>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      {/* <div className="flex items-center gap-3 mb-6">
                <div className="w-10 h-10 bg-secondary/10 rounded-lg flex items-center justify-center">
                    <Mail className="w-5 h-5 text-secondary" />
                </div>
                <div>
                    <h3 className="text-lg font-semibold text-gray-900">Compose Email</h3>
                    <p className="text-sm text-gray-600">Send a custom email to your customer</p>
                </div>
            </div> */}

      {/* Email Form */}
      <div className="space-y-4">
        {/* To Field */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            To <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="email"
              value={formData.to}
              disabled
              onChange={(e) => handleInputChange("to", e.target.value)}
              placeholder="<EMAIL>"
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent transition-all duration-200 hover:border-gray-400 disabled:bg-gray-100 disabled:cursor-not-allowed"
            />
          </div>
        </div>

        {/* Subject Field */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Subject <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            value={formData.subject}
            onChange={(e) => handleInputChange("subject", e.target.value)}
            placeholder="Enter email subject"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent transition-all duration-200 hover:border-gray-400"
          />
        </div>

        {/* CC Field */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            CC <span className="text-gray-400">(optional)</span>
          </label>
          <input
            type="email"
            value={formData.cc}
            onChange={(e) => handleInputChange("cc", e.target.value)}
            placeholder="<EMAIL> (separate multiple emails with commas)"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent transition-all duration-200 hover:border-gray-400"
          />
        </div>

        {/* BCC Field */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            BCC <span className="text-gray-400">(optional)</span>
          </label>
          <input
            type="email"
            value={formData.bcc}
            onChange={(e) => handleInputChange("bcc", e.target.value)}
            placeholder="<EMAIL> (separate multiple emails with commas)"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent transition-all duration-200 hover:border-gray-400"
          />
        </div>

        {/* Rich Text Editor */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Message <span className="text-red-500">*</span>
          </label>

          {/* Formatting Toolbar */}
          <div className="border border-gray-300 rounded-t-lg bg-gray-50 p-2 flex items-center gap-1 shadow-sm">
            <button
              type="button"
              onClick={() => formatText("bold")}
              className="p-2 rounded hover:bg-white hover:shadow-sm transition-all duration-200"
              title="Bold"
            >
              <Bold className="w-4 h-4 text-gray-600" />
            </button>
            <button
              type="button"
              onClick={() => formatText("italic")}
              className="p-2 rounded hover:bg-white hover:shadow-sm transition-all duration-200"
              title="Italic"
            >
              <Italic className="w-4 h-4 text-gray-600" />
            </button>
            <button
              type="button"
              onClick={() => formatText("underline")}
              className="p-2 rounded hover:bg-white hover:shadow-sm transition-all duration-200"
              title="Underline"
            >
              <Underline className="w-4 h-4 text-gray-600" />
            </button>
            {/* <div className="w-px h-6 bg-gray-300 mx-1"></div> */}
            {/* <button
                            type="button"
                            onClick={() => formatText('insertUnorderedList')}
                            className="p-2 rounded hover:bg-white hover:shadow-sm transition-all duration-200"
                            title="Bullet List"
                        >
                            <List className="w-4 h-4 text-gray-600" />
                        </button>
                        <button
                            type="button"
                            onClick={() => formatText('insertOrderedList')}
                            className="p-2 rounded hover:bg-white hover:shadow-sm transition-all duration-200"
                            title="Numbered List"
                        >
                            <ListOrdered className="w-4 h-4 text-gray-600" />
                        </button> */}
          </div>

          {/* Content Editable Area */}
          <ContentEditable
            innerRef={contentEditableRef}
            html={formData.body}
            onChange={handleBodyChange}
            onBlur={handleBodyBlur}
            onClick={() => setIsBodyEditing(true)}
            className={`w-full min-h-[200px] p-4 border border-gray-300 border-t-0 rounded-b-lg focus:outline-none transition-all duration-200 resize-none ${
              isBodyEditing ? "bg-white shadow-sm" : "bg-gray-50"
            }`}
            placeholder="Type your message here..."
          />
        </div>

        {/* Future Attachments Section */}
        <div className="border-2 border-dashed border-gray-200 rounded-lg p-4 text-center">
          <Paperclip className="w-6 h-6 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-500">Attachments (Coming Soon)</p>
          <p className="text-xs text-gray-400">
            File attachment functionality will be available in a future update
          </p>
        </div>

        {/* Send Button */}
        <div className="flex justify-end pt-4">
          <button
            onClick={handleSendEmail}
            disabled={
              isSending || !formData.to || !formData.subject || !formData.body
            }
            className="flex items-center gap-2 px-6 py-3 bg-secondary text-white rounded-lg hover:bg-secondary/90 hover:shadow-lg  disabled:cursor-not-allowed disabled:shadow-none transition-all duration-200 font-medium transform disabled:transform-none disabled:bg-gray-300"
          >
            <Send className={`w-4 h-4 ${isSending ? "animate-pulse" : ""}`} />
            {isSending ? "Sending..." : "Send Email"}
          </button>
        </div>

        {/* Error Display */}
        {sendError && (
          <div className="mt-4">
            <ComponentNote isError={true}>
              {getUserFriendlyErrorMessage(sendError)}
            </ComponentNote>
          </div>
        )}
      </div>
    </div>
  );
};

export default ComponentEmailComposition;
