'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Building2 } from 'lucide-react';
import { useCompanyContext } from '../auth/ComponentRouteProtection';
import CompanyBasicDetailsSetting from '@/component/company/settings/ComponentBasicDetailsSetting';
import ComponentLoading from '@/component/common/ComponentLoading';

export const ComponentCompanyCreationForm: React.FC = () => {
    const { hasCompany, isLoading: isCompanyLoading, retry } = useCompanyContext();
    const [isLoading, setIsLoading] = useState(true);

    const router = useRouter();

    useEffect(() => {
        if (isCompanyLoading) {
            setIsLoading(true);
        } else {
            setIsLoading(false);
            if (hasCompany) {
                router.replace('/');
            }
        }
    }, [hasCompany, isCompanyLoading]);

    const refreshOnSave = () => {
        router.replace('/home');
    }


    // Show loading state while company verification is in progress
    if (isLoading) {
        return (
            <div className="min-h-screen bg-gradient-primary">
                <ComponentLoading
                    message="Checking your account..."
                    className="min-h-screen"
                />
            </div>
        );
    }

    if (hasCompany) {
        return (
            <div className="min-h-screen bg-gradient-primary flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                    <h2 className="text-xl font-semibold text-gray-900 mb-2">Redirecting...</h2>
                    <p className="text-gray-600">Redirecting to your dashboard.</p>
                </div>
            </div>
        );
    }

    return (
        <div className="max-w-4xl mx-auto">
            {/* <div className="mb-8 max-w-2xl mx-auto">
                <AIInsightsPanel />
            </div> */}

            <div className="text-center mb-8">
                <div className="bg-primary/10 rounded-full h-16 w-16 flex items-center justify-center mx-auto bg-primary mb-6 p-2">
                    <Building2 className="h-8 w-8 text-white" />
                </div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">Create Your Company</h1>
                <p className="text-gray-600 max-w-2xl mx-auto">
                    To get started with Oplatz, we need some basic information about your company.
                    This will help us customize your experience and set up your workspace.
                </p>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-8 mb-8">
                <CompanyBasicDetailsSetting editMode={true} initialCreate={true} onSubmitSuccess={() => {
                    refreshOnSave();
                }} />
            </div>

            {/* Help Text */}
            <div className="text-center mt-8">
                <p className="text-sm text-gray-500">
                    Need help? Contact our support team for assistance with setting up your company.
                </p>
            </div>
        </div>
    );
};
