'use client';

import React, { useEffect, useState, useRef } from 'react';
import { set, useFormContext } from 'react-hook-form';
import { FileText, PenBoxIcon, Pencil } from 'lucide-react';
import { ProposalFormData } from '@/types/component/proposals/TypeProposal';
import ContentEditable from 'react-contenteditable';
import { ContentEditableEvent } from 'react-contenteditable';
import sanitizeHtml from 'sanitize-html';
import ComponentDocumentUpload, { DocumentInput } from '@/component/common/ComponentDocumentUpload';
import { ExtendedDocumentInput } from '@/types/component/catalog/TypeProduct';
import { DocumentType } from '@/lib/graphql/types/generated/graphql';


const ComponentProposalAdditionalDetails: React.FC = () => {
    const { register, watch, setValue, formState: { errors } } = useFormContext<ProposalFormData>();

    const notes = watch('notes') || [];
    const files = watch('documents') || [];

    const initialContent = notes.length > 0 ? notes[0]?.content || '' : '';
    const [noteContent, setNoteContent] = useState(initialContent);
    const [notesEditing, setNotesEditing] = useState(false);
    const contentEditableRef = useRef(null);

    useEffect(() => {
        setNoteContent(initialContent);
    }, [initialContent]);


    const handleNotesChange = (e: ContentEditableEvent) => {
        const noteContent = sanitizeHtml(e.target.value);
        setNoteContent(noteContent);
    };

    const handleBlur = () => {
        const ref = (contentEditableRef.current as any)
        const noteContent = ref.props.html;
        const updatedNotes = [{
            id: notes[0]?.id,
            content: noteContent,
            tags: [] // For future expansion
        }];
        setValue('notes', updatedNotes);
        setNotesEditing(false);

    };

    return (
        <div>
            <div className="mb-6">
                <h2 className="text-xl font-bold text-gray-900 mb-2">Additional Details</h2>
                <p className="text-gray-600">Notes and additional information for the proposal</p>
            </div>

            <div className="space-y-6">
                {/* Notes Section */}
                <div className={`bg-white border border-gray-200 p-4 rounded-lg`}>
                    <label className="flex items-center gap-2 mb-3">
                        <FileText className="h-4 w-4 text-secondary" />
                        <span>Notes</span>
                    </label>

                    <div className={`${notesEditing ? 'bg-white' : 'bg-gray-50'}`}>
                        <ContentEditable
                            ref={contentEditableRef}
                            html={noteContent}
                            onBlur={() => { handleBlur(); }}
                            onChange={handleNotesChange}
                            onClick={() => { setNotesEditing(true) }}
                            className='focus:outline-none border border-gray-300 p-2 rounded-md resize-vertical min-h-[120px]'
                        />
                    </div>
                    <div className='mt-5'>
                        <ComponentDocumentUpload value={files.filter((doc, index, self) =>
                            index === self.findIndex((d) => d.id === doc.id)
                        ) as DocumentInput[]}
                            onDocumentsChange={(files: ExtendedDocumentInput[]) => setValue('documents', files)}
                            documentType={DocumentType.Quote}
                            classes='text-sm font-normal' />
                    </div>
                </div>
            </div>
        </div >
    );
};

export default ComponentProposalAdditionalDetails;
