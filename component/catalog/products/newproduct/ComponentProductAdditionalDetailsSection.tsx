import React, { useState, useEffect } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import ComponentDocumentUpload, { DocumentInput } from '@/component/common/ComponentDocumentUpload';
import { ComponentCustomTagsInput } from '@/component/common/ComponentCustomTagInput';
import { SaveIcon } from 'lucide-react';
import { useMasterProductUpsertAdditionalDetailsMutation } from '@/lib/graphql/types/generated/hooks';
import {
    MasterProductUpsertAdditionalDetailsInput,
    CustomTagType,
    DocumentType
} from '@/lib/graphql/types/generated/graphql';
import { getUserFriendlyErrorMessage } from '@/lib/graphql/utils/errorHandling';
import { ApolloError } from '@apollo/client';
import ComponentNote from '@/component/common/ComponentNote';
import { ExtendedDocumentInput, ProductCustomTagInput } from '@/types/component/catalog/TypeProduct';

// Form data interface for additional details
interface ProductAdditionalDetailsFormData {
    documents: ExtendedDocumentInput[];
    customTags: ProductCustomTagInput[];
}

interface ComponentProductAdditionalDetailsSectionProps {
    productId: string;
    initialDocuments?: ExtendedDocumentInput[];
    initialCustomTags?: ProductCustomTagInput[];
    onSaveSuccess?: () => void;
}

const ComponentProductAdditionalDetailsSection: React.FC<ComponentProductAdditionalDetailsSectionProps> = ({
    productId,
    initialDocuments = [],
    initialCustomTags = [],
    onSaveSuccess
}) => {
    const [isLoading, setIsLoading] = useState(false);
    const [saveNote, setSaveNote] = useState<React.ReactNode | null>(null);

    const [masterProductUpsertAdditionalDetails] = useMasterProductUpsertAdditionalDetailsMutation();

    // Set up React Hook Form with initial data and validation
    const methods = useForm<ProductAdditionalDetailsFormData>({
        defaultValues: {
            documents: initialDocuments,
            customTags: initialCustomTags
        },
        mode: 'onChange' // Enable real-time validation
    });

    const { handleSubmit, watch, setValue } = methods;

    // Watch form values for real-time updates
    const documents = watch('documents');
    const customTags = watch('customTags');

    // Update form when initial data changes (for edit mode)
    useEffect(() => {
        methods.reset({
            documents: initialDocuments,
            customTags: initialCustomTags
        });
    }, [initialDocuments, initialCustomTags, methods]);

    const mapToMasterProductUpsertAdditionalDetailsInput = (data: ProductAdditionalDetailsFormData): MasterProductUpsertAdditionalDetailsInput => {
        const successfulDocumentIds = data.documents
            .filter(doc => doc.id && doc.uploadSuccess) // Only documents that were successfully uploaded
            .map(doc => doc.id!);

        // Convert component CustomTagInput to GraphQL CustomTagInput
        const graphqlCustomTags = data.customTags.map(tag => ({
            id: tag.id || null, // Existing tags have IDs, new ones don't
            key: tag.key,
            label: tag.label,
            value: tag.value,
            type: tag.type as CustomTagType, // Convert string to enum
            description: tag.description
        }));

        return {
            masterProductId: productId,
            documents: successfulDocumentIds, // Send only document IDs, not full objects
            customTags: graphqlCustomTags
        };
    };

    const handleSaveAdditionalDetails = async (data: ProductAdditionalDetailsFormData) => {
        setIsLoading(true);
        setSaveNote(null);

        // Basic validation for custom tags
        const invalidTags = data.customTags.filter(tag =>
            tag.label.trim() === '' || tag.value.trim() === ''
        );

        if (invalidTags.length > 0) {
            setIsLoading(false);
            setSaveNote(<ComponentNote isError={true}>Please fill in all required fields for custom tags (label and value).</ComponentNote>);
            return;
        }

        // Check for documents that have files but haven't been uploaded yet
        const documentsWithFiles = data.documents.filter(doc => doc.file);
        const pendingDocuments = documentsWithFiles.filter(doc => !doc.uploadSuccess || !doc.id);

        if (pendingDocuments.length > 0) {
            setIsLoading(false);
            setSaveNote(<ComponentNote isError={true}>
                Please upload all documents before saving. Click the "Upload" button on each document card to upload them.
            </ComponentNote>);
            return;
        }

        // Check for documents that are currently uploading
        const uploadingDocuments = data.documents.filter(doc => doc.isUploading);
        if (uploadingDocuments.length > 0) {
            setIsLoading(false);
            setSaveNote(<ComponentNote isError={true}>Please wait for all documents to finish uploading before saving.</ComponentNote>);
            return;
        }

        // Check for documents with upload errors
        const failedDocuments = data.documents.filter(doc => doc.file && doc.uploadError);
        if (failedDocuments.length > 0) {
            setIsLoading(false);
            setSaveNote(<ComponentNote isError={true}>
                Some documents failed to upload. Please fix the errors or remove the failed documents before saving.
            </ComponentNote>);
            return;
        }

        try {
            // Save additional details with successfully uploaded document IDs
            setSaveNote(<ComponentNote>Saving additional details...</ComponentNote>);

            const mutationResult = await masterProductUpsertAdditionalDetails({
                variables: {
                    input: mapToMasterProductUpsertAdditionalDetailsInput(data)
                }
            });

            if (mutationResult.data?.masterProductUpsertAdditionalDetails) {
                setSaveNote(<ComponentNote>Additional details saved successfully!</ComponentNote>);
                onSaveSuccess?.();
            }
        } catch (error) {
            console.error('Error saving additional details:', error);
            setSaveNote(<ComponentNote isError={true}>
                {getUserFriendlyErrorMessage(error as ApolloError)}
            </ComponentNote>);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="mt-12">
            <div className="mb-4">
                <h2 className="text-2xl font-bold text-gray-900">Additional Details</h2>
                <p className="text-sm text-gray-600">Attach documents and add custom tags to enrich your product information.</p>
            </div>

            {saveNote && (
                <div className="mb-6">
                    {saveNote}
                </div>
            )}

            <FormProvider {...methods}>
                <form onSubmit={handleSubmit(handleSaveAdditionalDetails)} className="space-y-8">
                    <div className="bg-white rounded-lg border border-gray-200 p-6 flex flex-col space-y-8">
                        <ComponentDocumentUpload
                            value={documents as DocumentInput[]}
                            onDocumentsChange={(docs: ExtendedDocumentInput[]) => setValue('documents', docs)}
                            documentType={DocumentType.MasterProduct}
                            classes='text-lg font-semibold'
                        />
                        <ComponentCustomTagsInput
                            value={customTags as any}
                            onTagsChange={(tags) => setValue('customTags', tags as ProductCustomTagInput[])}
                        />
                        <div className="flex justify-end col-span-2 mx-5">
                            <button
                                type="submit"
                                disabled={isLoading}
                                className="px-6 py-2 bg-primary text-white rounded-lg hover:bg-opacity-90 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <div className='flex items-center gap-2'>
                                    <SaveIcon className='h-4 w-4' />
                                    {isLoading ? 'Saving...' : 'Save Details'}
                                </div>
                            </button>
                        </div>
                    </div>
                </form>
            </FormProvider>
        </div >
    );
};

export default ComponentProductAdditionalDetailsSection;