import React, { useState, useEffect } from 'react';
import {
    Upload, Trash2, Plus, Edit3, Check, FileText, AlertCircle,
    Download, Loader2, CheckCircle, XCircle,
    X,
    File,
    ScrollText,
    Folder
} from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';
import { useGeneratePresignedWriteUrlMutation } from '@/lib/graphql/types/generated/hooks';
import { DocumentType } from '@/lib/graphql/types/generated/graphql';
import { ExtendedDocumentInput } from '@/types/component/catalog/TypeProduct';
import ComponentNote from './ComponentNote';

// Re-export the type for backward compatibility
export type DocumentInput = ExtendedDocumentInput;

type Props = {
    value?: ExtendedDocumentInput[];
    onDocumentsChange?: (docs: ExtendedDocumentInput[]) => void;
    error?: string;
    documentType?: DocumentType;
    classes?: string;
};

// File validation constants
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const BLOCKED_VIDEO_EXTENSIONS = [
    '.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v', '.3gp', '.ogv'
];
const BLOCKED_MIME_TYPES = [
    'video/mp4', 'video/avi', 'video/quicktime', 'video/x-msvideo', 'video/x-flv',
    'video/webm', 'video/x-matroska', 'video/3gpp', 'video/ogg'
];

// File validation function
const validateFile = (file: File): string | null => {
    if (!file) return 'No file selected';

    if (file.size > MAX_FILE_SIZE) {
        return `File size exceeds 10MB limit. Current size: ${(file.size / (1024 * 1024)).toFixed(2)}MB`;
    }

    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (BLOCKED_VIDEO_EXTENSIONS.includes(fileExtension)) {
        return 'Video files are not allowed';
    }

    if (BLOCKED_MIME_TYPES.includes(file.type)) {
        return 'Video files are not allowed';
    }

    return null;
};

// Default document template
const createDefaultDocument = (): ExtendedDocumentInput => ({
    requestId: uuidv4(),
    label: '',
    description: '',
    tags: [],
    uploadProgress: 0,
    uploadSuccess: false,
    isUploading: false,
    presignedUrlGenerated: false
});

// S3 Upload utility function
const uploadFileToS3 = async (
    file: File,
    signedWriteURL: string,
    onProgress?: (progress: number) => void
): Promise<void> => {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();

        // Set up progress tracking
        xhr.upload.addEventListener('progress', (event) => {
            if (event.lengthComputable && onProgress) {
                const progress = Math.round((event.loaded / event.total) * 100);
                onProgress(progress);
            }
        });

        // Handle successful upload
        xhr.addEventListener('load', () => {
            if (xhr.status >= 200 && xhr.status < 300) {
                resolve();
            } else {
                reject(new Error(`Upload failed with status ${xhr.status}`));
            }
        });

        // Handle errors
        xhr.addEventListener('error', () => {
            reject(new Error('Network error during upload'));
        });

        xhr.addEventListener('abort', () => {
            reject(new Error('Upload was cancelled'));
        });

        // Configure and send request
        xhr.open('PUT', signedWriteURL);
        xhr.setRequestHeader('Content-Type', file.type || 'application/octet-stream');
        xhr.send(file);
    });
};



// Main Component
const ComponentDocumentUpload: React.FC<Props> = ({
    value = [],
    onDocumentsChange,
    error,
    documentType = DocumentType.MasterProduct,
    classes = null,
}) => {
    const documents = value.map(doc => ({ ...doc, uploadSuccess: true, presignedUrlGenerated: true }));
    const [editingIndexes, setEditingIndexes] = useState<number[]>([]);
    const [generatePresignedUrl] = useGeneratePresignedWriteUrlMutation();
    const [globalMessage, setGlobalMessage] = useState<{ type: 'success' | 'error', message: string } | null>(null);

    // Auto-clear global messages after 5 seconds
    useEffect(() => {
        if (globalMessage) {
            const timer = setTimeout(() => {
                setGlobalMessage(null);
            }, 5000);
            return () => clearTimeout(timer);
        }
    }, [globalMessage]);

    const handleAddDocument = () => {
        const newDoc = createDefaultDocument();
        const newDocs = [...documents, newDoc];
        onDocumentsChange?.(newDocs);
        setEditingIndexes([...editingIndexes, documents.length]);
    };

    const handleRemoveDocument = (idx: number) => {
        const newDocs = documents.filter((_, index) => index !== idx);
        onDocumentsChange?.(newDocs);
        setEditingIndexes(editingIndexes.filter(editIdx => editIdx !== idx).map(editIdx => editIdx > idx ? editIdx - 1 : editIdx));
    };

    const handleDocumentChange = (idx: number, updates: Partial<ExtendedDocumentInput>) => {
        const newDocs = [...documents];
        newDocs[idx] = { ...newDocs[idx], ...updates };
        onDocumentsChange?.(newDocs);
    };

    const handleFileSelect = (idx: number, file: File) => {
        // Validate file first
        const validationError = validateFile(file);
        if (validationError) {
            handleDocumentChange(idx, { uploadError: validationError });
            return;
        }

        // Update document with file and clear any previous errors
        // Set default label to file name if not already set
        const currentDoc = documents[idx];
        handleDocumentChange(idx, {
            file,
            label: currentDoc.label || file.name, // Use file name as default label
            uploadError: undefined,
            uploadProgress: 0,
            uploadSuccess: false,
            presignedUrlGenerated: false,
            isUploading: false
        });
    };

    const handleDocumentUpload = async (idx: number) => {
        const doc = documents[idx];

        if (!doc.file) {
            handleDocumentChange(idx, { uploadError: 'No file selected' });
            return;
        }

        if (!doc.label || doc.label.trim() === '') {
            handleDocumentChange(idx, { uploadError: 'Document label is required' });
            return;
        }

        // Start upload process
        handleDocumentChange(idx, {
            isUploading: true,
            uploadError: undefined,
            uploadProgress: 0
        });

        try {
            // Generate presigned URL
            const result = await generatePresignedUrl({
                variables: {
                    input: {
                        requestId: doc.requestId || uuidv4(),
                        type: documentType,
                        label: doc.label,
                        description: doc.description,
                        tags: doc.tags || []
                    }
                }
            });

            if (result.data?.generatePresignedWriteURL?.file?.signedWriteURL && result.data.generatePresignedWriteURL.id) {
                const signedWriteURL = result.data.generatePresignedWriteURL.file.signedWriteURL;
                const documentId = result.data.generatePresignedWriteURL.id;

                // Update document with presigned URL info
                handleDocumentChange(idx, {
                    id: documentId,
                    signedWriteURL,
                    presignedUrlGenerated: true
                });

                // Start S3 upload
                await uploadFileToS3(doc.file, signedWriteURL, (progress) => {
                    handleDocumentChange(idx, { uploadProgress: progress });
                });

                // Upload successful
                handleDocumentChange(idx, {
                    id: documentId,
                    isUploading: false,
                    uploadSuccess: true,
                    uploadProgress: 100
                });

                setGlobalMessage({ type: 'success', message: `Document "${doc.label}" uploaded successfully!` });
            } else {
                throw new Error('Failed to generate presigned URL');
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Upload failed';
            handleDocumentChange(idx, {
                isUploading: false,
                uploadError: errorMessage,
                presignedUrlError: errorMessage
            });

            setGlobalMessage({ type: 'error', message: `Failed to upload "${doc.label}": ${errorMessage}` });
        }
    };

    const toggleEdit = (idx: number) => {

        if (editingIndexes.includes(idx)) {
            setEditingIndexes(editingIndexes.filter(editIdx => editIdx !== idx));
        } else {
            setEditingIndexes([...editingIndexes, idx]);
        }

        const doc = documents[idx];
        if (!!doc.file) {
            doc.file = null
        }

        if (!doc.id && !doc.file) {
            handleRemoveDocument(idx);
        }
    };

    // Auto-exit edit mode after successful upload
    const handleDocumentUploadWithEditToggle = async (idx: number) => {
        await handleDocumentUpload(idx);
        // Check if upload was successful and exit edit mode
        const doc = documents[idx];
        if (doc.uploadSuccess && doc.id) {
            toggleEdit(idx);
        }
    };

    const getUploadStatusIcon = (doc: ExtendedDocumentInput) => {
        if (doc.isUploading) {
            return <Loader2 className="w-4 h-4 animate-spin text-blue-500" />;
        }
        if (doc.uploadSuccess) {
            return <CheckCircle className="w-4 h-4 text-green-500" />;
        }
        if (doc.uploadError) {
            return <XCircle className="w-4 h-4 text-red-500" />;
        }
        return null;
    };

    return (
        <div>
            <div className="flex gap-2 mb-2 items-center">
                <Folder className="h-4 w-4 text-secondary" />
                <span className={`text-lg text-gray-900 ${classes || ''}`}>Documents</span>
                <button
                    type="button"
                    onClick={handleAddDocument}
                    className="text-white hover:bg-opacity-90 text-xs bg-secondary p-1 rounded-full cursor-pointer transition-all"
                >
                    <Plus className='h-4 w-4' />
                </button>
            </div>
            <div className="text-gray-500 text-sm mb-4">
                Upload product-related documents (e.g., spec sheets, images, manuals). Maximum file size: 10MB. Video files are not allowed.
            </div>

            {/* Display global error if any */}
            {error && (
                <ComponentNote isError>
                    {error}
                </ComponentNote>
            )}

            {/* Global Message */}
            {globalMessage && (
                <ComponentNote isError={globalMessage.type === 'error'}>
                    {globalMessage.message}
                </ComponentNote>
            )}

            {/* Document Cards */}
            <div className="mt-2 flex flex-row gap-2 flex-wrap">
                {documents.map((doc, idx) => (
                    <DocumentCard
                        key={doc.requestId || doc.id}
                        document={doc}
                        index={idx}
                        isEditing={editingIndexes.includes(idx)}
                        onFileSelect={(file) => handleFileSelect(idx, file)}
                        onDocumentChange={(updates) => handleDocumentChange(idx, updates)}
                        onRemove={() => handleRemoveDocument(idx)}
                        onToggleEdit={() => toggleEdit(idx)}
                        onUpload={() => handleDocumentUploadWithEditToggle(idx)}
                        getUploadStatusIcon={getUploadStatusIcon}
                    />
                ))}
            </div>

            {/* Empty state */}
            {documents.length === 0 && (
                <div className="border border-dashed border-gray-300 rounded-lg p-8 text-center text-gray-400">
                    <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No documents added yet.</p>
                    <p className="text-xs mt-1">Click the + button to add your first document.</p>
                </div>
            )}
        </div>
    );
};

// DocumentCard component for individual document display
interface DocumentCardProps {
    document: ExtendedDocumentInput;
    index: number;
    isEditing: boolean;
    onFileSelect: (file: File) => void;
    onDocumentChange: (updates: Partial<ExtendedDocumentInput>) => void;
    onRemove: () => void;
    onToggleEdit: () => void;
    onUpload: () => void;
    getUploadStatusIcon: (doc: ExtendedDocumentInput) => React.ReactNode;
}

const DocumentCard: React.FC<DocumentCardProps> = ({
    document: doc,
    isEditing,
    onFileSelect,
    onDocumentChange,
    onRemove,
    onToggleEdit,
    onUpload,
    getUploadStatusIcon
}) => {
    const hasFile = !!doc.file;
    const isUploading = doc.isUploading;
    const uploadProgress = doc.uploadProgress || 0;
    const hasError = !!doc.uploadError;
    const isUploaded = doc.uploadSuccess && doc.id;

    const downloadFile = (doc: ExtendedDocumentInput) => {
        if (doc.signedReadURL) {
            // download the file
            window.open(doc.signedReadURL, '_blank');
            // window.location.href = doc.signedReadURL;
        }
    };

    return (
        <div className={`${isEditing ? 'bg-white' : 'bg-gray-50'} border border-gray-200 rounded-lg p-2 hover:shadow-md transition-all duration-200`}>
            <div className="flex flex-col items-start gap-2">
                <div className="flex items-start space-x-3 flex-1">
                    <div className="flex-1">
                        {isEditing ? (
                            <div className="space-y-3 w-64">
                                <div>
                                    <div className='flex justify-end mb-2'>
                                        <button onClick={onToggleEdit} className='p-1 text-gray-400 hover:text-red-600 transition-colors cursor-pointer'>
                                            <X className="h-4 w-4" />
                                        </button>
                                    </div>

                                    {
                                        (doc?.label && doc.id) && (
                                            <p className="text-xs text-gray-500 mt-1 mb-1 truncate">
                                                Uploaded File: {doc.label}
                                            </p>
                                        )
                                    }
                                    <input
                                        type="file"
                                        required
                                        disabled={doc.id != null}
                                        onChange={(e) => {
                                            const file = e.target.files?.[0];
                                            if (file) onFileSelect(file);
                                        }}
                                        title={doc.id ? 'Cannot change file for existing document' : 'Select file to upload'}
                                        className="block w-full text-sm border border-gray-300 rounded-lg text-black file:py-2 file:rounded-lg file:border file:text-sm file:font-medium file:bg-primary file:text-white hover:file:bg-opacity-90 cursor-pointer focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                                    />


                                    {(hasFile && !doc.id) && (
                                        <p className="text-xs text-gray-500 mt-1">
                                            File to upload: {doc.file?.name} ({((doc.file?.size || 0) / (1024 * 1024)).toFixed(2)} MB)
                                        </p>
                                    )}
                                </div>

                                <div>
                                    <input
                                        type="text"
                                        required
                                        title={doc.id ? 'Cannot change label for existing document' : 'Enter document label'}
                                        disabled={doc.id != null}
                                        placeholder="Document label"
                                        value={doc.label || ''}
                                        onChange={(e) => onDocumentChange({ label: e.target.value })}
                                        className="w-full px-2 py-2 border border-gray-300 rounded-lg focus:outline-none text-sm disabled:cursor-not-allowed disabled:opacity-50"
                                    />
                                </div>

                                {/* <div>
                                    <input
                                        type="text"
                                        placeholder="Description (optional)"
                                        value={doc.description || ''}
                                        onChange={(e) => onDocumentChange({ description: e.target.value })}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none text-sm"
                                    />
                                </div> */}

                                {/* <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Tags
                                    </label>
                                    <input
                                        type="text"
                                        placeholder="Tags (comma-separated)"
                                        value={doc.tags?.join(', ') || ''}
                                        onChange={(e) => {
                                            const tags = e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag);
                                            onDocumentChange({ tags });
                                        }}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent text-sm"
                                    />
                                </div> */}
                            </div>
                        ) : (
                            <div>
                                <div className="font-normal text-gray-900 truncate w-48 h-10">
                                    {doc.label || doc.file?.name || 'Untitled Document'}
                                </div>
                                {/* {doc.description && (
                                    <p className="text-sm text-gray-500 mt-1">{doc.description}</p>
                                )} */}

                                {/* {doc.label && (
                                    <p className="text-xs text-gray-400 w-48 mt-1 truncate">
                                        {doc.label}
                                    </p>
                                )} */}
                                {/* {doc.tags && doc.tags.length > 0 && (
                                    <div className="flex flex-wrap gap-1 mt-2">
                                        {doc.tags.map((tag, tagIdx) => (
                                            <span key={tagIdx} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-700">
                                                {tag}
                                            </span>
                                        ))}
                                    </div>
                                )} */}
                            </div>
                        )}

                        {/* Upload Progress */}
                        {isUploading && (
                            <div className="mt-3">
                                <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
                                    <span>Uploading...</span>
                                    <span>{uploadProgress}%</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                    <div
                                        className="bg-primary h-2 rounded-full transition-all duration-300"
                                        style={{ width: `${uploadProgress}%` }}
                                    ></div>
                                </div>
                            </div>
                        )}


                        {/* Error Display with Retry */}
                        {hasError && (
                            <div className="mt-2">
                                <div className="text-sm text-red-600 flex items-center gap-2 mb-2">
                                    <AlertCircle className="h-4 w-4" />
                                    {doc.uploadError}
                                </div>
                                {/* {hasFile && (
                                    <button
                                        type="button"
                                        onClick={onUpload}
                                        disabled={isUploading}
                                        className="text-xs bg-red-50 text-red-600 hover:bg-red-100 px-2 py-1 rounded border border-red-200 disabled:opacity-50"
                                    >
                                        Retry Upload
                                    </button>
                                )} */}
                            </div>
                        )}
                    </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-row gap-2 w-full">
                    {/* Upload Status Icon */}
                    <div className='mt-1' hidden={!doc.uploadError}>
                        {getUploadStatusIcon(doc)}
                    </div>

                    {/* Download Button */}
                    {doc.signedReadURL && (
                        <button
                            type="button"
                            onClick={() => downloadFile(doc)}
                            className="text-gray-400 hover:text-primary transition-colors p-1 w-full"
                            title="Download"
                        >
                            <Download className="h-4 w-4 cursor-pointer hover:text-blue-600" />
                        </button>
                    )}

                    {/* Edit/Save Button */}
                    {isEditing ? (
                        <button
                            type="button"
                            onClick={() => {
                                if (hasFile && !isUploaded) {
                                    // If there's a file that hasn't been uploaded, upload it
                                    onUpload();
                                } else {
                                    // Otherwise just toggle edit mode
                                    onToggleEdit();
                                }
                            }}
                            disabled={isUploading}
                            className="text-white bg-primary hover:bg-opacity-90 cursor-pointer rounded-md p-1 transition-colors w-full disabled:opacity-50 disabled:cursor-not-allowed"
                            title={hasFile && !isUploaded ? "Upload Document" : "Save"}
                        >
                            <div className='flex items-center gap-2 justify-center'>
                                {isUploading ? (
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                ) : hasFile && !isUploaded ? (
                                    <Upload className="h-4 w-4" />
                                ) : (
                                    <Check className="h-4 w-4" />
                                )}
                            </div>
                        </button>
                    ) : (
                        <button
                            hidden={doc.id !== null}
                            type="button"
                            onClick={onToggleEdit}
                            className="text-white bg-primary hover:bg-opacity-90 cursor-pointer rounded-md p-1 transition-colors w-full"
                            title="Edit"
                        >
                            <div className='flex items-center gap-2 justify-center'>
                                <Edit3 className="h-4 w-4" />
                            </div>
                        </button>
                    )}

                    {/* Delete Button */}
                    <button
                        type="button"
                        onClick={onRemove}
                        className="bg-red-500/30 text-red-500 hover:bg-opacity-90 cursor-pointer rounded-md p-1 transition-colors w-full"
                        title="Delete"
                    >
                        <div className='flex items-center gap-2 justify-center'>
                            <Trash2 className="h-4 w-4" />
                        </div>
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ComponentDocumentUpload;
