"use client"
import Link from 'next/link';
import { Bell, Users, Settings, Home, BriefcaseBusiness, Receipt, ReceiptText } from 'lucide-react';
import { usePathname } from 'next/navigation';
import { UserButton } from '@clerk/nextjs';
import ComponentTooltip from '@/component/common/ComponentTooltip';
import { useCompanyContext } from '../auth/ComponentRouteProtection';


const Navbar = () => {
    const pathname = usePathname();
    const { hasCompany, isLoading, company, retry } = useCompanyContext();

    const getLinkStyles = (path: string) => {
        const isActive = (path === '/' && pathname === path) || (path !== '/' && pathname.startsWith(path));
        return isActive
            ? 'font-semibold rounded-xl text-white bg-primary shadow-md transform scale-105 transition-all duration-200'
            : 'text-gray hover:text-primary hover:bg-white-60-opacity hover:shadow-sm hover:scale-105 transition-all duration-200 rounded-xl';
    };

    const getIconStyles = (path: string) => {
        const isActive = (path === '/' && pathname === path) || (path !== '/' && pathname.startsWith(path));
        return isActive
            ? 'text-white'
            : 'text-primary group-hover:text-primary transition-colors duration-200';
    };

    return (
        <nav className='rounded-2xl mx-4 my-3 transition-all duration-300'>
            <div className="mx-auto px-6 py-4">
                <div className="flex justify-between items-center">
                    {/* Logo Section */}
                    <div className="flex items-center">
                        <Link href="/home" className="flex items-center group">
                            <img src='/logo.png' alt="Logo" className="h-18 w-18 mr-2" />
                        </Link>
                    </div>

                    {/* Navigation Items */}
                    {hasCompany && (
                        <div className="flex items-center bg-gray-100/40 shadow-sm  rounded-2xl p-2 space-x-2 backdrop-blur-sm gap-2">
                            <ComponentTooltip
                                content="Home"
                                position="bottom"
                                delay={200}
                            >
                                <Link href="/home" className={`${getLinkStyles('/home')} px-5 py-3 flex items-center gap-3 group font-medium`} aria-label="Home Dashboard">
                                    <Home className={`h-5 w-5 ${getIconStyles('/home')}`} />
                                    <span className="hidden md:block">Home</span>
                                </Link>
                            </ComponentTooltip>
                            <ComponentTooltip
                                content="Customer Management"
                                position="bottom"
                                delay={200}
                            >
                                <Link href="/customers" className={`${getLinkStyles('/customers')} px-5 py-3 flex items-center gap-3 group font-medium`} aria-label="Customer Management">
                                    <Users className={`h-5 w-5 ${getIconStyles('/customers')}`} />
                                    <span className="hidden md:block">Customers</span>
                                </Link>
                            </ComponentTooltip>
                            <ComponentTooltip
                                content="Proposals & Catalog"
                                position="bottom"
                                delay={200}
                            >
                                <Link href="/catalog/proposals" className={`${getLinkStyles('/catalog')} px-5 py-3 flex items-center gap-3 group font-medium`} aria-label="Proposals & Catalog">
                                    <ReceiptText className={`h-5 w-5 ${getIconStyles('/catalog')}`} />
                                    <span className="hidden md:block">Proposals</span>
                                </Link>
                            </ComponentTooltip>

                            <ComponentTooltip
                                content="Company Settings"
                                position="bottom"
                                delay={200}
                            >
                                <Link href="/company/settings/team" className={`${getLinkStyles('/company/settings')} px-5 py-3 flex items-center gap-3 group font-medium`} aria-label="Company Settings">
                                    <Settings className={`h-5 w-5 ${getIconStyles('/company/settings')}`} />
                                </Link>
                            </ComponentTooltip>
                        </div>
                    )}

                    {/* User Section */}
                    <div className="flex items-center space-x-3">
                        {/* User Avatar */}
                        <ComponentTooltip
                            content="User Profile"
                            position="bottom"
                            delay={200}
                        >
                            <div className="flex items-center">
                                <div className="h-10 w-10 rounded-xl p-2 shadow-md hover:shadow-lg transition-all duration-200">
                                    <div className="h-full w-full rounded-xl bg-white flex items-center justify-center">
                                        <UserButton />
                                    </div>
                                </div>
                            </div>
                        </ComponentTooltip>
                    </div>
                </div>
            </div>
        </nav>
    );
};

export default Navbar;
