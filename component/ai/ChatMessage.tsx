import React from 'react';
import { Bot, User } from 'lucide-react';
import { ChatMessage as ChatMessageType } from '@/types/component/ai/TypeAIInsightsModal';

interface ChatMessageProps {
    message: ChatMessageType;
}

const ChatMessage: React.FC<ChatMessageProps> = ({ message }) => {
    const isUser = message.sender === 'user';

    return (
        <div className={`flex items-start gap-3 my-3 ${isUser ? 'justify-end' : 'justify-start'}`}>
            {!isUser && (
                <div className="w-8 h-8 rounded-full bg-secondary flex items-center justify-center flex-shrink-0">
                    <Bot className="w-5 h-5 text-white" />
                </div>
            )}
            {isUser && (
                <div className="w-8 h-8 rounded-full border bg-primary flex items-center justify-center flex-shrink-0">
                    <User className="w-5 h-5 text-white" />
                </div>
            )}
            <div
                className={`rounded-2xl px-4 py-2.5 text-sm max-w-[85%] md:max-w-[75%] whitespace-pre-wrap ${isUser
                    ? 'bg-white border border-gray-100 text-gray-800-white rounded-br-none'
                    : 'bg-gray-100 text-gray-800 border border-gray-100 rounded-bl-none'
                    }`}
            >
                <p>{message.text}</p>
                <div className={`text-xs mt-1.5 ${isUser ? 'text-gray-200/70' : 'text-gray-400'}`}>
                    {message.timestamp}
                </div>
            </div>
        </div>
    );
};

export default ChatMessage; 