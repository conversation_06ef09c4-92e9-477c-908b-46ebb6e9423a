/* Secondary Vertical Navbar Styles */

.secondary-navbar {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 6.2rem;
    background-color: rgb(243 244 246 / 0.4);
    padding: 0.5rem;
    height: fit-content;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}

.secondary-navbar-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 0.5rem;
    color: var(--gray);
    cursor: pointer;
    transition: all 0.1s ease-in-out;
    text-decoration: none;
    border-radius: 0.5rem;
}

.secondary-navbar-item:hover {
    color: var(--secondary-orange);
}

.secondary-navbar-item.active {
    color: var(--white);
    background-color: var(--secondary-orange);
    border-right-color: var(--secondary-orange);
    font-weight: 600;
    border-radius: 10px;
}

.secondary-navbar-icon {
    width: 1.25rem;
    height: 1.25rem;
    flex-shrink: 0;
}

.secondary-navbar-layout {
    display: flex;
    flex-direction: row;
    gap: 3rem;
}

.secondary-navbar-content {
    flex: 1;
    /* margin-bottom: 2rem; */
    overflow-y: auto;
    height: 100vh;
    padding: 1rem;
    scrollbar-width: none;
}

/* Responsive behavior */
@media (max-width: 768px) {
    .secondary-navbar {
        width: 100%;
        min-width: 100%;
        flex-direction: row;
        overflow-x: auto;
    }

    .secondary-navbar-item {
        white-space: nowrap;
        min-width: fit-content;
    }

    .secondary-navbar-item.active {
        border-right-color: transparent;
    }

    .secondary-navbar-layout {
        flex-direction: column;
        height: 100vh;
    }
}