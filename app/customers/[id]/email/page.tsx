"use client";

import React from "react";
import ComponentCustomerDetailLayout from "@/component/customers/customerdetails/ComponentCustomerDetailLayout";
import { useParams } from "next/navigation";
import ComponentCustomerEmail from "@/component/customers/customerdetails/ComponentCustomerEmail";

const CustomerEmailPage = () => {
  const params = useParams();
  const customerId = params.id as string;

  return (
    <ComponentCustomerDetailLayout>
      <div className="max-w-6xl mx-auto mb-10">
        <ComponentCustomerEmail customerId={customerId} />
      </div>
    </ComponentCustomerDetailLayout>
  );
};

export default CustomerEmailPage;
