'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import ComponentCustomerDetailLayout from '@/component/customers/customerdetails/ComponentCustomerDetailLayout';
import ComponentCustomerProposals from '@/component/customers/customerdetails/ComponentCustomerProposals';

const CustomerProposalsPage = () => {
    const params = useParams();
    const customerId = params.id as string;

    return (
        <ComponentCustomerDetailLayout>
            <div className="mx-auto">
                <ComponentCustomerProposals customerId={customerId} />
            </div>
        </ComponentCustomerDetailLayout>
    );
};

export default CustomerProposalsPage;
