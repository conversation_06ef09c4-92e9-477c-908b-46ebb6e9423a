'use client';

import React, { useEffect } from 'react';
import ComponentAIChatInterface from '@/component/ai/ComponentAIChatInterface';
import { useCompanyContext } from '@/component/auth/ComponentRouteProtection';
import WelcomeHero from '@/component/widgets/WelcomeHero';

export default function HomePage() {

    const { hasCompany, company, companyUser, retry } = useCompanyContext();

    useEffect(() => {
        if (!hasCompany) {
            retry();
        }
    }, [hasCompany, retry]);

    return (
        /**Needs to occupy full screen with chat taking most of the space */
        <div className="flex flex-col max-w-6xl mx-auto h-screen">
            <div className="mt-10 item-center">
                <WelcomeHero userName={companyUser?.name || ''} companyName={company?.name || ''} />
            </div>
            <div className="max-w-5xl mx-auto mt-10 mb-50 border rounded-lg border-gray-200 h-full overflow-hidden">
                <div className="h-full">
                    <ComponentAIChatInterface className="h-full" />
                </div>
                {/* <div className="rounded-lg">
                    <div className="h-full overflow-hidden">
                        <ComponentInsightsCarousel />
                    </div>
                </div> */}
            </div>
        </div>
    );
}
