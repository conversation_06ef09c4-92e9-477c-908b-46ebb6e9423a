import { gql } from "@apollo/client";

export const CUSTOMER_SEND_EMAIL_MUTATION = gql`
  mutation CustomerSendEmail($input: CustomerSendEmailInput!) {
    customerSendEmail(input: $input) {
      success
      message
    }
  }
`;

export const CUSTOMER_SEND_EMAIL_BULK_MUTATION = gql`
  mutation CustomerSendEmailBulk($input: CustomerSendEmailBulkInput!) {
    customerSendEmailBulk(input: $input) {
      success
      message
      totalRequested
      totalQueued
      failedCustomers
      jobId
    }
  }
`;
