import { gql } from "@apollo/client";

export const GET_ALL_PROPOSALS = gql`
    query GetAllProposals($status: QuoteStatus, $quoteId: ID) {
        quotesGet(
            filters: {
                status: $status
                quoteId: $quoteId
            }
        ) {
            id
            description
            customer {
                id
                ...on CustomerBusiness {
                    basicDetails {
                        legalName
                        contactDetails {
                            email
                            phoneNo
                        } 
                    }
                }
                ...on CustomerIndividual {
                    basicDetails {
                        contactDetails {
                            name
                            email
                            phoneNo
                        }
                    }
                }
            }
            status
            version
            assignments {
                salesExecutive {
                    id
                    name
                }
                customerSuccessManger {
                    id
                    name
                }
            }
            date {
                validFrom
                validTill
            }
            currency
            quoteTotalSellingPrice {
                value
                currency
            }
            quoteTotalTaxAmount {
                value
                currency
            }
            quoteTotalDiscountAmount {
                value
                currency
            }
            
        }
    }
`;



export const GET_ALL_PROPOSALS_WITH_PRODUCTS = gql`
    query GetAllProposalsWithProducts($status: QuoteStatus, $quoteId: ID) {
        quotesGet(
            filters: {
                status: $status
                quoteId: $quoteId
            }
        ) {
      id
      description
      customer {
        id
        ... on CustomerBusiness {
          basicDetails {
            legalName
            contactDetails {
              name
              email
              phoneNo
            }
          }
        }
        ... on CustomerIndividual {
          basicDetails {
            contactDetails {
              name
              email
              phoneNo
            }
          }
        }
      }
      products {
        id
        productType
        productCode
        name
        description
        status
        version
        dimensions {
          key
          value
        }
        pricing {
          id
          chargePolicy
          tax {
            name
            percentage
            amount {
              value
              currency
            }
          }
          discount {
            id
            description
            discountType
            discountValue {
              value {
                  value
                  currency
              }
              percentage
            }
            discountLevel
          }
          costPrice {
            value
            currency
          }
          listPrice {
            value
            currency
          }
          sellingPrice {
            value
            currency
          }
          margin {
            percentage
            absoluteAmount {
              value
              currency
            }
          }
          unit {
            unit
            unitType
          }
        }
      }
      status
      version
      assignments {
        salesExecutive {
          id
          name
        }
        customerSuccessManger {
          id
          name
        }
      }
      date {
        validFrom
        validTill
      }
      currency
      quoteTotalSellingPrice {
        value
        currency
      }
      quoteTotalTaxAmount {
        value
        currency
      }
      quoteTotalDiscountAmount {
        value
        currency
      }
      address {
        fromAddress
        toAddress
      }
      notes {
        id
        content
        tags
      }
      documents {
                id
                label
                description
                file {
                    signedReadURL
                    signedWriteURL
                }
        
      }
    }
  }
`;

export const DOWNLOAD_PROPOSAL_DOCUMENT = gql` 
  query DownloadProposalDocument($quoteId: ID!) {
    quotesGet(
      filters: {
        quoteId: $quoteId
      }
    ) {
      id
      files {
        name
        ... on HTMLEncodedFile {
          name
          fileType
          base64Encoded
        }
      }
    }
  }
`;