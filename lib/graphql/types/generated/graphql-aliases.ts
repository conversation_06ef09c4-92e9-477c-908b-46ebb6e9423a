export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = {
  [K in keyof T]: T[K];
};
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]?: Maybe<T[SubKey]>;
};
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]: Maybe<T[SubKey]>;
};
export type MakeEmpty<
  T extends { [key: string]: unknown },
  K extends keyof T,
> = { [_ in K]?: never };
export type Incremental<T> =
  | T
  | {
      [P in keyof T]?: P extends " $fragmentName" | "__typename" ? T[P] : never;
    };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string };
  String: { input: string; output: string };
  Boolean: { input: boolean; output: boolean };
  Int: { input: number; output: number };
  Float: { input: number; output: number };
  Date: { input: any; output: any };
  DateTime: { input: any; output: any };
  _FieldSet: { input: any; output: any };
};

export type Amount = {
  readonly __typename?: "Amount";
  readonly currency: Currency;
  readonly value: Scalars["Float"]["output"];
};

export type AmountInput = {
  readonly currency: Currency;
  readonly value: Scalars["Float"]["input"];
};

/**  Company user ids */
export type AssignmentInput = {
  readonly accountManager?: InputMaybe<Scalars["ID"]["input"]>;
  readonly supportRepresentative?: InputMaybe<Scalars["ID"]["input"]>;
};

export type BusinessCustomerBasicDetails = {
  readonly __typename?: "BusinessCustomerBasicDetails";
  readonly address?: Maybe<Scalars["String"]["output"]>;
  readonly contactDetails: ContactDetails;
  readonly industry?: Maybe<Scalars["String"]["output"]>;
  readonly legalName: Scalars["String"]["output"];
  readonly plainTags?: Maybe<ReadonlyArray<Scalars["String"]["output"]>>;
  readonly referralSource?: Maybe<Scalars["String"]["output"]>;
  readonly size: EntitySize;
  readonly website: Scalars["String"]["output"];
};

export type BusinessCustomerBasicDetailsInput = {
  readonly address?: InputMaybe<Scalars["String"]["input"]>;
  readonly contactPersonDetails: ContactPersonDetailsInput;
  readonly industry?: InputMaybe<Scalars["String"]["input"]>;
  readonly legalName: Scalars["String"]["input"];
  readonly plainTags?: InputMaybe<ReadonlyArray<Scalars["String"]["input"]>>;
  readonly referralSource?: InputMaybe<Scalars["String"]["input"]>;
  readonly size: EntitySize;
  readonly website: Scalars["String"]["input"];
};

export enum ChargePolicy {
  Unit = "UNIT",
}

export type Company = {
  readonly __typename?: "Company";
  readonly basicDetails?: Maybe<CompanyBasicDetails>;
  readonly id: Scalars["ID"]["output"];
  readonly name?: Maybe<Scalars["String"]["output"]>;
  readonly onboarding?: Maybe<CompanyOnboarding>;
  readonly status?: Maybe<CompanyStatus>;
  readonly users?: Maybe<ReadonlyArray<Maybe<CompanyUser>>>;
};

export type CompanyBasicDetails = {
  readonly __typename?: "CompanyBasicDetails";
  readonly address?: Maybe<Scalars["String"]["output"]>;
  readonly email: Scalars["String"]["output"];
  readonly industry?: Maybe<Scalars["String"]["output"]>;
  readonly name: Scalars["String"]["output"];
  readonly phoneNumber?: Maybe<Scalars["String"]["output"]>;
  readonly size?: Maybe<EntitySize>;
  readonly website: Scalars["String"]["output"];
};

export type CompanyCreateBasicDetailsInput = {
  readonly address?: InputMaybe<Scalars["String"]["input"]>;
  readonly email: Scalars["String"]["input"];
  readonly industry?: InputMaybe<Scalars["String"]["input"]>;
  readonly name: Scalars["String"]["input"];
  readonly phoneNumber?: InputMaybe<Scalars["String"]["input"]>;
  readonly size?: InputMaybe<EntitySize>;
  readonly website: Scalars["String"]["input"];
};

export type CompanyEmail = Email & {
  readonly __typename?: "CompanyEmail";
  readonly attachments?: Maybe<ReadonlyArray<Scalars["String"]["output"]>>;
  readonly bcc?: Maybe<ReadonlyArray<Scalars["String"]["output"]>>;
  /**  optional as the template can support this as well */
  readonly body?: Maybe<Scalars["String"]["output"]>;
  readonly cc?: Maybe<ReadonlyArray<Scalars["String"]["output"]>>;
  /**  optional as the template can support this as well */
  readonly recipients: ReadonlyArray<Scalars["String"]["output"]>;
  readonly subject?: Maybe<Scalars["String"]["output"]>;
  readonly template: CompanyEmailTemplate;
  readonly type: EmailType;
};

export type CompanyEmailTemplate = EmailTemplate & {
  readonly __typename?: "CompanyEmailTemplate";
  readonly body: Scalars["String"]["output"];
  readonly fromName?: Maybe<Scalars["String"]["output"]>;
  readonly subject: Scalars["String"]["output"];
  readonly type: CompanyEmailTemplateType;
};

export enum CompanyEmailTemplateType {
  CompanyUserInvitationEmail = "COMPANY_USER_INVITATION_EMAIL",
  CompanyWelcomeEmail = "COMPANY_WELCOME_EMAIL",
}

export type CompanyOnboarding = {
  readonly __typename?: "CompanyOnboarding";
  readonly completedSteps: ReadonlyArray<Maybe<CompanyOnboardingStep>>;
  readonly id: Scalars["ID"]["output"];
  readonly pendingSteps: ReadonlyArray<Maybe<CompanyOnboardingStep>>;
};

export type CompanyOnboardingStep = {
  readonly __typename?: "CompanyOnboardingStep";
  readonly completed: Scalars["Boolean"]["output"];
  readonly mandatory: Scalars["Boolean"]["output"];
  readonly stepType: CompanyOnboardingStepType;
};

export enum CompanyOnboardingStepType {
  AccountingDetails = "ACCOUNTING_DETAILS",
  BasicDetails = "BASIC_DETAILS",
  CatalogDetails = "CATALOG_DETAILS",
  CompanyUsersDetails = "COMPANY_USERS_DETAILS",
}

export enum CompanyStatus {
  Active = "ACTIVE",
  Created = "CREATED",
  Inactive = "INACTIVE",
  Onboarding = "ONBOARDING",
}

export type CompanyUpdateBasicDetailsInput = {
  readonly address?: InputMaybe<Scalars["String"]["input"]>;
  readonly companyId: Scalars["ID"]["input"];
  readonly industry?: InputMaybe<Scalars["String"]["input"]>;
  readonly phoneNumber?: InputMaybe<Scalars["String"]["input"]>;
  readonly size?: InputMaybe<EntitySize>;
  readonly website: Scalars["String"]["input"];
};

export type CompanyUser = {
  readonly __typename?: "CompanyUser";
  readonly company: Company;
  readonly email: Scalars["String"]["output"];
  readonly id: Scalars["ID"]["output"];
  readonly name: Scalars["String"]["output"];
  readonly phoneNumber?: Maybe<Scalars["String"]["output"]>;
  readonly roles: ReadonlyArray<Maybe<CompanyUserRole>>;
  readonly status: CompanyUserStatus;
};

/**  input financial settings */
export type CompanyUserCreateInput = {
  readonly companyId: Scalars["ID"]["input"];
  readonly email: Scalars["String"]["input"];
  readonly name: Scalars["String"]["input"];
  readonly roles: ReadonlyArray<CompanyUserRole>;
};

export enum CompanyUserRole {
  AccountExecutive = "ACCOUNT_EXECUTIVE",
  Admin = "ADMIN",
  CharteredAccountant = "CHARTERED_ACCOUNTANT",
  CustomerSuccessManager = "CUSTOMER_SUCCESS_MANAGER",
}

export enum CompanyUserStatus {
  Active = "ACTIVE",
  Created = "CREATED",
  Inactive = "INACTIVE",
  Invited = "INVITED",
}

export type ContactDetails = {
  readonly __typename?: "ContactDetails";
  readonly contactType: ContactType;
  readonly email: Scalars["String"]["output"];
  readonly name: Scalars["String"]["output"];
  readonly phoneNo?: Maybe<Scalars["String"]["output"]>;
  readonly title?: Maybe<Scalars["String"]["output"]>;
};

export type ContactPersonDetailsInput = {
  readonly contactType: ContactType;
  readonly email: Scalars["String"]["input"];
  readonly name: Scalars["String"]["input"];
  readonly phoneNo?: InputMaybe<Scalars["String"]["input"]>;
  readonly title?: InputMaybe<Scalars["String"]["input"]>;
};

export enum ContactType {
  Business = "BUSINESS",
  Person = "PERSON",
}

export enum Currency {
  Aed = "AED",
  Inr = "INR",
  Usd = "USD",
}

export type CustomTag = {
  readonly __typename?: "CustomTag";
  readonly description?: Maybe<Scalars["String"]["output"]>;
  readonly id?: Maybe<Scalars["ID"]["output"]>;
  /**  to support client dto transformations */
  readonly key: Scalars["String"]["output"];
  readonly label: Scalars["String"]["output"];
  readonly type: CustomTagType;
  readonly value: Scalars["String"]["output"];
};

export type CustomTagInput = {
  readonly description?: InputMaybe<Scalars["String"]["input"]>;
  readonly id?: InputMaybe<Scalars["ID"]["input"]>;
  readonly key: Scalars["String"]["input"];
  readonly label: Scalars["String"]["input"];
  readonly type: CustomTagType;
  readonly value: Scalars["String"]["input"];
};

export enum CustomTagType {
  Boolean = "BOOLEAN",
  Date = "DATE",
  Numeric = "NUMERIC",
  Select = "SELECT",
  String = "STRING",
}

export type Customer = {
  readonly assignments: ReadonlyArray<Maybe<CustomerAssignment>>;
  readonly company: Company;
  readonly customTags: ReadonlyArray<Maybe<CustomTag>>;
  readonly documents: ReadonlyArray<Maybe<Document>>;
  readonly id: Scalars["ID"]["output"];
  readonly notes?: Maybe<ReadonlyArray<Maybe<Notes>>>;
  readonly quotes?: Maybe<ReadonlyArray<Quote>>;
  readonly stage: CustomerStage;
  readonly status: CustomerStatus;
  readonly type: CustomerType;
};

export type CustomerAssignment = {
  readonly __typename?: "CustomerAssignment";
  readonly accountManager?: Maybe<CompanyUser>;
  readonly supportRepresentative?: Maybe<CompanyUser>;
};

export type CustomerBusiness = Customer & {
  readonly __typename?: "CustomerBusiness";
  readonly assignments: ReadonlyArray<Maybe<CustomerAssignment>>;
  readonly basicDetails: BusinessCustomerBasicDetails;
  readonly company: Company;
  readonly customTags: ReadonlyArray<Maybe<CustomTag>>;
  readonly documents: ReadonlyArray<Maybe<Document>>;
  readonly id: Scalars["ID"]["output"];
  readonly notes?: Maybe<ReadonlyArray<Maybe<Notes>>>;
  readonly plainTags?: Maybe<ReadonlyArray<Scalars["String"]["output"]>>;
  readonly quotes?: Maybe<ReadonlyArray<Quote>>;
  readonly stage: CustomerStage;
  readonly status: CustomerStatus;
  readonly type: CustomerType;
};

export type CustomerIndividual = Customer & {
  readonly __typename?: "CustomerIndividual";
  readonly assignments: ReadonlyArray<Maybe<CustomerAssignment>>;
  readonly basicDetails: IndividualCustomerBasicDetails;
  readonly company: Company;
  readonly customTags: ReadonlyArray<Maybe<CustomTag>>;
  readonly documents: ReadonlyArray<Maybe<Document>>;
  readonly id: Scalars["ID"]["output"];
  readonly notes?: Maybe<ReadonlyArray<Maybe<Notes>>>;
  readonly quotes?: Maybe<ReadonlyArray<Quote>>;
  readonly stage: CustomerStage;
  readonly status: CustomerStatus;
  readonly type: CustomerType;
};

export type CustomerProduct = Product & {
  readonly __typename?: "CustomerProduct";
  /** User provided, can be null */
  readonly company: Company;
  readonly customTags?: Maybe<ReadonlyArray<CustomTag>>;
  readonly customer: Customer;
  readonly description?: Maybe<Scalars["String"]["output"]>;
  readonly dimensions?: Maybe<ReadonlyArray<Dimension>>;
  readonly documents?: Maybe<ReadonlyArray<Document>>;
  readonly id: Scalars["ID"]["output"];
  readonly name: Scalars["String"]["output"];
  readonly pricing: Pricing;
  readonly productCode?: Maybe<Scalars["String"]["output"]>;
  readonly productType: ProductType;
  readonly quote?: Maybe<Quote>;
  readonly status: ProductStatus;
  /** or quoteProduct? */
  readonly tax?: Maybe<ReadonlyArray<Tax>>;
  readonly version: Scalars["ID"]["output"];
};

export type CustomerProductToggleActivationInput = {
  readonly id: Scalars["ID"]["input"];
  readonly version?: InputMaybe<Scalars["ID"]["input"]>;
};

export type CustomerProductUpdateInput = {
  /**  Associated documents */
  readonly customTags?: InputMaybe<ReadonlyArray<CustomTagInput>>;
  /**  User provided unique identifier, can be null */
  readonly customerId: Scalars["ID"]["input"];
  /**  Required as unique identifier along with dimensions */
  readonly description?: InputMaybe<Scalars["String"]["input"]>;
  readonly dimensions?: InputMaybe<ReadonlyArray<DimensionInput>>;
  /**  Pricing information */
  readonly discount: DiscountInput;
  /**  Product dimensions/variants */
  readonly documents?: InputMaybe<ReadonlyArray<DocumentInput>>;
  readonly id: Scalars["ID"]["input"];
  /** To the code it's tagged to. */
  readonly name: Scalars["String"]["input"];
  /**  Custom tags for categorization */
  readonly pricing: PricingInput;
  readonly productCode?: InputMaybe<Scalars["String"]["input"]>;
  /**  Required to identify the customer */
  readonly quoteId: Scalars["ID"]["input"];
};

export enum CustomerStage {
  Churned = "CHURNED",
  ConversionFailed = "CONVERSION_FAILED",
  ConversionInProgress = "CONVERSION_IN_PROGRESS",
  Converted = "CONVERTED",
  Lead = "LEAD",
  Prospect = "PROSPECT",
}

export enum CustomerStatus {
  Active = "ACTIVE",
  Suspended = "SUSPENDED",
}

export enum CustomerType {
  Business = "BUSINESS",
  Individual = "INDIVIDUAL",
}

export type CustomerUpsertAdditionalDetailsInput = {
  readonly assignments?: InputMaybe<ReadonlyArray<AssignmentInput>>;
  readonly customTags?: InputMaybe<ReadonlyArray<CustomTagInput>>;
  readonly customerId: Scalars["ID"]["input"];
  readonly documents?: InputMaybe<ReadonlyArray<DocumentInput>>;
  readonly notes?: InputMaybe<ReadonlyArray<NotesInput>>;
};

export type CustomerUpsertBasicDetailsInput = {
  readonly businessCustomerDetails?: InputMaybe<BusinessCustomerBasicDetailsInput>;
  readonly customerId?: InputMaybe<Scalars["ID"]["input"]>;
  readonly customerStage: CustomerStage;
  readonly customerType: CustomerType;
  readonly individualCustomerDetails?: InputMaybe<IndividualCustomerBasicDetailsInput>;
};

export type Dimension = {
  readonly __typename?: "Dimension";
  readonly key: Scalars["String"]["output"];
  readonly value: Scalars["String"]["output"];
};

export type DimensionInput = {
  readonly key: Scalars["String"]["input"];
  readonly value: Scalars["String"]["input"];
};

export type Discount = {
  readonly __typename?: "Discount";
  /**
   *  Auto generated
   * name: String!
   */
  readonly description?: Maybe<Scalars["String"]["output"]>;
  readonly discountLevel: DiscountLevel;
  readonly discountType: DiscountType;
  readonly discountValue: DiscountValue;
  readonly id: Scalars["ID"]["output"];
};

export type DiscountInput = {
  readonly discountLevel: DiscountLevel;
  readonly discountType: DiscountType;
  readonly discountValue: DiscountValueInput;
};

export enum DiscountLevel {
  Product = "PRODUCT",
  Quote = "QUOTE",
}

export enum DiscountType {
  Amount = "AMOUNT",
  Percentage = "PERCENTAGE",
}

export type DiscountValue = {
  readonly __typename?: "DiscountValue";
  readonly percentage: Scalars["Float"]["output"];
  readonly value: Amount;
};

export type DiscountValueInput = {
  readonly amount: AmountInput;
  readonly percentage: Scalars["Float"]["input"];
};

export type Document = {
  readonly __typename?: "Document";
  readonly description?: Maybe<Scalars["String"]["output"]>;
  readonly file?: Maybe<FileLink>;
  readonly id?: Maybe<Scalars["ID"]["output"]>;
  readonly label?: Maybe<Scalars["String"]["output"]>;
  readonly tags: ReadonlyArray<Scalars["String"]["output"]>;
};

export type DocumentInput = {
  readonly file?: InputMaybe<FileLinkInput>;
  readonly id?: InputMaybe<Scalars["ID"]["input"]>;
};

export type Email = {
  readonly attachments?: Maybe<ReadonlyArray<Scalars["String"]["output"]>>;
  readonly bcc?: Maybe<ReadonlyArray<Scalars["String"]["output"]>>;
  /**  optional as the template can support this as well */
  readonly body?: Maybe<Scalars["String"]["output"]>;
  readonly cc?: Maybe<ReadonlyArray<Scalars["String"]["output"]>>;
  /**  optional as the template can support this as well */
  readonly recipients: ReadonlyArray<Scalars["String"]["output"]>;
  readonly subject?: Maybe<Scalars["String"]["output"]>;
  readonly template: EmailTemplate;
  readonly type: EmailType;
};

export type EmailTemplate = {
  readonly body: Scalars["String"]["output"];
  readonly fromName?: Maybe<Scalars["String"]["output"]>;
  readonly subject: Scalars["String"]["output"];
};

export enum EmailType {
  CompanyEmail = "COMPANY_EMAIL",
  OplatzEmail = "OPLATZ_EMAIL",
}

export enum EntitySize {
  GreaterThanThousand = "GREATER_THAN_THOUSAND",
  HundredToThousand = "HUNDRED_TO_THOUSAND",
  LessThanTen = "LESS_THAN_TEN",
  TenToHundred = "TEN_TO_HUNDRED",
  Unknown = "UNKNOWN",
}

export enum ErrorDetail {
  /**
   * The deadline expired before the operation could complete.
   *
   * For operations that change the state of the system, this error
   * may be returned even if the operation has completed successfully.
   * For example, a successful response from a server could have been
   * delayed long enough for the deadline to expire.
   *
   * HTTP Mapping: 504 Gateway Timeout
   * Error Type: UNAVAILABLE
   */
  DeadlineExceeded = "DEADLINE_EXCEEDED",
  /**
   * The server detected that the client is exhibiting a behavior that
   * might be generating excessive load.
   *
   * HTTP Mapping: 420 Enhance Your Calm
   * Error Type: UNAVAILABLE
   */
  EnhanceYourCalm = "ENHANCE_YOUR_CALM",
  /**
   * The requested field is not found in the schema.
   *
   * This differs from `NOT_FOUND` in that `NOT_FOUND` should be used when a
   * query is valid, but is unable to return a result (if, for example, a
   * specific video id doesn't exist). `FIELD_NOT_FOUND` is intended to be
   * returned by the server to signify that the requested field is not known to exist.
   * This may be returned in lieu of failing the entire query.
   * See also `PERMISSION_DENIED` for cases where the
   * requested field is invalid only for the given user or class of users.
   *
   * HTTP Mapping: 404 Not Found
   * Error Type: BAD_REQUEST
   */
  FieldNotFound = "FIELD_NOT_FOUND",
  /**
   * The client specified an invalid argument.
   *
   * Note that this differs from `FAILED_PRECONDITION`.
   * `INVALID_ARGUMENT` indicates arguments that are problematic
   * regardless of the state of the system (e.g., a malformed file name).
   *
   * HTTP Mapping: 400 Bad Request
   * Error Type: BAD_REQUEST
   */
  InvalidArgument = "INVALID_ARGUMENT",
  /**
   * The provided cursor is not valid.
   *
   * The most common usage for this error is when a client is paginating
   * through a list that uses stateful cursors. In that case, the provided
   * cursor may be expired.
   *
   * HTTP Mapping: 404 Not Found
   * Error Type: NOT_FOUND
   */
  InvalidCursor = "INVALID_CURSOR",
  /**
   * Unable to perform operation because a required resource is missing.
   *
   * Example: Client is attempting to refresh a list, but the specified
   * list is expired. This requires an action by the client to get a new list.
   *
   * If the user is simply trying GET a resource that is not found,
   * use the NOT_FOUND error type. FAILED_PRECONDITION.MISSING_RESOURCE
   * is to be used particularly when the user is performing an operation
   * that requires a particular resource to exist.
   *
   * HTTP Mapping: 400 Bad Request or 500 Internal Server Error
   * Error Type: FAILED_PRECONDITION
   */
  MissingResource = "MISSING_RESOURCE",
  /**
   * Service Error.
   *
   * There is a problem with an upstream service.
   *
   * This may be returned if a gateway receives an unknown error from a service
   * or if a service is unreachable.
   * If a request times out which waiting on a response from a service,
   * `DEADLINE_EXCEEDED` may be returned instead.
   * If a service returns a more specific error Type, the specific error Type may
   * be returned instead.
   *
   * HTTP Mapping: 502 Bad Gateway
   * Error Type: UNAVAILABLE
   */
  ServiceError = "SERVICE_ERROR",
  /**
   * Request failed due to network errors.
   *
   * HTTP Mapping: 503 Unavailable
   * Error Type: UNAVAILABLE
   */
  TcpFailure = "TCP_FAILURE",
  /**
   * Request throttled based on server concurrency limits.
   *
   * HTTP Mapping: 503 Unavailable
   * Error Type: UNAVAILABLE
   */
  ThrottledConcurrency = "THROTTLED_CONCURRENCY",
  /**
   * Request throttled based on server CPU limits
   *
   * HTTP Mapping: 503 Unavailable.
   * Error Type: UNAVAILABLE
   */
  ThrottledCpu = "THROTTLED_CPU",
  /**
   * The server detected that the client is exhibiting a behavior that
   * might be generating excessive load.
   *
   * HTTP Mapping: 429 Too Many Requests
   * Error Type: UNAVAILABLE
   */
  TooManyRequests = "TOO_MANY_REQUESTS",
  /**
   * The operation is not implemented or is not currently supported/enabled.
   *
   * HTTP Mapping: 501 Not Implemented
   * Error Type: BAD_REQUEST
   */
  Unimplemented = "UNIMPLEMENTED",
  /**
   * Unknown error.
   *
   * This error should only be returned when no other error detail applies.
   * If a client sees an unknown errorDetail, it will be interpreted as UNKNOWN.
   *
   * HTTP Mapping: 500 Internal Server Error
   */
  Unknown = "UNKNOWN",
}

export enum ErrorType {
  /**
   * Bad Request.
   *
   * There is a problem with the request.
   * Retrying the same request is not likely to succeed.
   * An example would be a query or argument that cannot be deserialized.
   *
   * HTTP Mapping: 400 Bad Request
   */
  BadRequest = "BAD_REQUEST",
  /**
   * The operation was rejected because the system is not in a state
   * required for the operation's execution.  For example, the directory
   * to be deleted is non-empty, an rmdir operation is applied to
   * a non-directory, etc.
   *
   * Service implementers can use the following guidelines to decide
   * between `FAILED_PRECONDITION` and `UNAVAILABLE`:
   *
   * - Use `UNAVAILABLE` if the client can retry just the failing call.
   * - Use `FAILED_PRECONDITION` if the client should not retry until
   * the system state has been explicitly fixed.  E.g., if an "rmdir"
   *      fails because the directory is non-empty, `FAILED_PRECONDITION`
   * should be returned since the client should not retry unless
   * the files are deleted from the directory.
   *
   * HTTP Mapping: 400 Bad Request or 500 Internal Server Error
   */
  FailedPrecondition = "FAILED_PRECONDITION",
  /**
   * Internal error.
   *
   * An unexpected internal error was encountered. This means that some
   * invariants expected by the underlying system have been broken.
   * This error code is reserved for serious errors.
   *
   * HTTP Mapping: 500 Internal Server Error
   */
  Internal = "INTERNAL",
  /**
   * The requested entity was not found.
   *
   * This could apply to a resource that has never existed (e.g. bad resource id),
   * or a resource that no longer exists (e.g. cache expired.)
   *
   * Note to server developers: if a request is denied for an entire class
   * of users, such as gradual feature rollout or undocumented allowlist,
   * `NOT_FOUND` may be used. If a request is denied for some users within
   * a class of users, such as user-based access control, `PERMISSION_DENIED`
   * must be used.
   *
   * HTTP Mapping: 404 Not Found
   */
  NotFound = "NOT_FOUND",
  /**
   * The caller does not have permission to execute the specified
   * operation.
   *
   * `PERMISSION_DENIED` must not be used for rejections
   * caused by exhausting some resource or quota.
   * `PERMISSION_DENIED` must not be used if the caller
   * cannot be identified (use `UNAUTHENTICATED`
   * instead for those errors).
   *
   * This error Type does not imply the
   * request is valid or the requested entity exists or satisfies
   * other pre-conditions.
   *
   * HTTP Mapping: 403 Forbidden
   */
  PermissionDenied = "PERMISSION_DENIED",
  /**
   * The request does not have valid authentication credentials.
   *
   * This is intended to be returned only for routes that require
   * authentication.
   *
   * HTTP Mapping: 401 Unauthorized
   */
  Unauthenticated = "UNAUTHENTICATED",
  /**
   * Currently Unavailable.
   *
   * The service is currently unavailable.  This is most likely a
   * transient condition, which can be corrected by retrying with
   * a backoff.
   *
   * HTTP Mapping: 503 Unavailable
   */
  Unavailable = "UNAVAILABLE",
  /**
   * Unknown error.
   *
   * For example, this error may be returned when
   * an error code received from another address space belongs to
   * an error space that is not known in this address space.  Also
   * errors raised by APIs that do not return enough error information
   * may be converted to this error.
   *
   * If a client sees an unknown errorType, it will be interpreted as UNKNOWN.
   * Unknown errors MUST NOT trigger any special behavior. These MAY be treated
   * by an implementation as being equivalent to INTERNAL.
   *
   * When possible, a more specific error should be provided.
   *
   * HTTP Mapping: 520 Unknown Error
   */
  Unknown = "UNKNOWN",
}

export type FileLink = {
  readonly __typename?: "FileLink";
  readonly signedReadURL?: Maybe<Scalars["String"]["output"]>;
  readonly signedWriteURL?: Maybe<Scalars["String"]["output"]>;
};

export type FileLinkInput = {
  readonly signedReadURL?: InputMaybe<Scalars["String"]["input"]>;
  readonly signedWriteURL?: InputMaybe<Scalars["String"]["input"]>;
};

export type GetCustomersQueryFilterInput = {
  readonly customerType?: InputMaybe<CustomerType>;
  readonly stage?: InputMaybe<CustomerStage>;
  readonly status?: InputMaybe<CustomerStatus>;
};

export type GetMasterProductFilters = {
  readonly status?: InputMaybe<ProductStatus>;
};

export type GetQuotesFilters = {
  readonly customer?: InputMaybe<QuoteCustomerFilter>;
  readonly quoteId?: InputMaybe<Scalars["ID"]["input"]>;
  readonly status?: InputMaybe<QuoteStatus>;
};

export type IndividualCustomerBasicDetails = {
  readonly __typename?: "IndividualCustomerBasicDetails";
  readonly address?: Maybe<Scalars["String"]["output"]>;
  readonly contactDetails: ContactDetails;
  readonly plainTags?: Maybe<ReadonlyArray<Scalars["String"]["output"]>>;
  readonly referralSource?: Maybe<Scalars["String"]["output"]>;
  readonly website?: Maybe<Scalars["String"]["output"]>;
};

export type IndividualCustomerBasicDetailsInput = {
  readonly address?: InputMaybe<Scalars["String"]["input"]>;
  readonly emailAddress: Scalars["String"]["input"];
  readonly fullName: Scalars["String"]["input"];
  readonly phoneNumber?: InputMaybe<Scalars["String"]["input"]>;
  readonly plainTags?: InputMaybe<ReadonlyArray<Scalars["String"]["input"]>>;
  readonly referralSource?: InputMaybe<Scalars["String"]["input"]>;
  readonly website?: InputMaybe<Scalars["String"]["input"]>;
};

export type Margin = {
  readonly __typename?: "Margin";
  readonly absoluteAmount: Amount;
  readonly percentage: Scalars["Float"]["output"];
};

export type MarginInput = {
  readonly absoluteAmount: AmountInput;
  readonly percentage: Scalars["Float"]["input"];
};

export type MasterProduct = Product & {
  readonly __typename?: "MasterProduct";
  /** User provided, can be null */
  readonly company: Company;
  readonly customTags?: Maybe<ReadonlyArray<CustomTag>>;
  readonly description?: Maybe<Scalars["String"]["output"]>;
  readonly dimensions?: Maybe<ReadonlyArray<Dimension>>;
  readonly documents?: Maybe<ReadonlyArray<Document>>;
  readonly id: Scalars["ID"]["output"];
  readonly name: Scalars["String"]["output"];
  readonly pricing: Pricing;
  readonly productCode?: Maybe<Scalars["String"]["output"]>;
  readonly productType: ProductType;
  readonly status: ProductStatus;
  readonly tax?: Maybe<ReadonlyArray<Tax>>;
  readonly version: Scalars["ID"]["output"];
};

export type MasterProductToggleActivationInput = {
  readonly id: Scalars["ID"]["input"];
  readonly version?: InputMaybe<Scalars["ID"]["input"]>;
};

export type MasterProductUpsertAdditionalDetailsInput = {
  readonly customTags?: InputMaybe<ReadonlyArray<CustomTagInput>>;
  readonly documents?: InputMaybe<ReadonlyArray<DocumentInput>>;
  readonly masterProductId: Scalars["ID"]["input"];
};

export type MasterProductUpsertInput = {
  readonly description?: InputMaybe<Scalars["String"]["input"]>;
  readonly dimensions?: InputMaybe<ReadonlyArray<DimensionInput>>;
  readonly id?: InputMaybe<Scalars["ID"]["input"]>;
  /**  will be computed in the backend if null */
  readonly name: Scalars["String"]["input"];
  readonly pricing: PricingInput;
  readonly productCode?: InputMaybe<Scalars["String"]["input"]>;
};

export type Mutation = {
  readonly __typename?: "Mutation";
  readonly companyCreateBasicDetails?: Maybe<Company>;
  readonly companyUpdateBasicDetails?: Maybe<Company>;
  readonly companyUserActivate: CompanyUser;
  readonly companyUserCreate: CompanyUser;
  readonly companyUserDeactivate: TaskResponse;
  readonly companyUserInvite: CompanyUser;
  readonly customerProductToggleActivation: CustomerProduct;
  readonly customerProductUpdate: CustomerProduct;
  readonly customerUpsertAdditionalDetails?: Maybe<Customer>;
  readonly customerUpsertBasicDetails?: Maybe<Customer>;
  readonly masterProductToggleActivation: MasterProduct;
  readonly masterProductUpsertAdditionalDetails: MasterProduct;
  readonly masterProductUpsertDetails: MasterProduct;
  readonly ok?: Maybe<Scalars["Boolean"]["output"]>;
  readonly quoteUpsert: Quote;
};

export type MutationCompanyCreateBasicDetailsArgs = {
  input: CompanyCreateBasicDetailsInput;
};

export type MutationCompanyUpdateBasicDetailsArgs = {
  input: CompanyUpdateBasicDetailsInput;
};

export type MutationCompanyUserCreateArgs = {
  input: CompanyUserCreateInput;
};

export type MutationCompanyUserDeactivateArgs = {
  companyUserId: Scalars["ID"]["input"];
};

export type MutationCompanyUserInviteArgs = {
  companyUserId: Scalars["ID"]["input"];
};

export type MutationCustomerProductToggleActivationArgs = {
  input: CustomerProductToggleActivationInput;
};

export type MutationCustomerProductUpdateArgs = {
  input: CustomerProductUpdateInput;
};

export type MutationCustomerUpsertAdditionalDetailsArgs = {
  input: CustomerUpsertAdditionalDetailsInput;
};

export type MutationCustomerUpsertBasicDetailsArgs = {
  input: CustomerUpsertBasicDetailsInput;
};

export type MutationMasterProductToggleActivationArgs = {
  input: MasterProductToggleActivationInput;
};

export type MutationMasterProductUpsertAdditionalDetailsArgs = {
  input: MasterProductUpsertAdditionalDetailsInput;
};

export type MutationMasterProductUpsertDetailsArgs = {
  input: MasterProductUpsertInput;
};

export type MutationQuoteUpsertArgs = {
  input: QuoteUpsertInput;
};

export type Notes = {
  readonly __typename?: "Notes";
  /**  to support client dto transformations */
  readonly content: Scalars["String"]["output"];
  readonly id?: Maybe<Scalars["ID"]["output"]>;
  readonly tags: ReadonlyArray<Scalars["String"]["output"]>;
};

export type NotesInput = {
  readonly content: Scalars["String"]["input"];
  readonly id?: InputMaybe<Scalars["ID"]["input"]>;
  readonly tags: ReadonlyArray<Scalars["String"]["input"]>;
};

export type OplatzEmail = Email & {
  readonly __typename?: "OplatzEmail";
  readonly attachments?: Maybe<ReadonlyArray<Scalars["String"]["output"]>>;
  readonly bcc?: Maybe<ReadonlyArray<Scalars["String"]["output"]>>;
  /**  optional as the template can support this as well */
  readonly body?: Maybe<Scalars["String"]["output"]>;
  readonly cc?: Maybe<ReadonlyArray<Scalars["String"]["output"]>>;
  /**  optional as the template can support this as well */
  readonly recipients: ReadonlyArray<Scalars["String"]["output"]>;
  readonly subject?: Maybe<Scalars["String"]["output"]>;
  readonly template: OplatzEmailTemplate;
  readonly type: EmailType;
};

export type OplatzEmailTemplate = EmailTemplate & {
  readonly __typename?: "OplatzEmailTemplate";
  readonly body: Scalars["String"]["output"];
  readonly fromName?: Maybe<Scalars["String"]["output"]>;
  readonly subject: Scalars["String"]["output"];
  readonly type: OplatzEmailTemplateType;
};

export enum OplatzEmailTemplateType {
  OplatzCompanyCreated = "OPLATZ_COMPANY_CREATED",
}

export type Pricing = {
  readonly __typename?: "Pricing";
  readonly chargePolicy: ChargePolicy;
  readonly costPrice: Amount;
  readonly discount: ReadonlyArray<Maybe<Discount>>;
  readonly id: Scalars["ID"]["output"];
  /**  price at which we buy from vendor or cost of rendering the service */
  readonly listPrice: Amount;
  /**  price @ which we sell to customer after discount, will be same as list price by default */
  readonly margin: Margin;
  /**  default price to sell at to customer */
  readonly sellingPrice: Amount;
  /**  unit of measurement for the product, e.g: 1 */
  readonly tax?: Maybe<ReadonlyArray<Tax>>;
  /**
   *  margin = (sellingPrice - costPrice) / sellingPrice
   * billingFrequency
   */
  readonly unit: ProductUnit;
};

export type PricingInput = {
  readonly chargePolicy: ChargePolicy;
  readonly costPrice: AmountInput;
  /**  unit of measurement for the product, e.g: 1 */
  readonly discounts?: InputMaybe<ReadonlyArray<DiscountInput>>;
  readonly id?: InputMaybe<Scalars["ID"]["input"]>;
  /**  price at which we buy from vendor or cost of rendering the service */
  readonly listPrice: AmountInput;
  /**  price @ which we sell to customer after discount, will be same as list price by default */
  readonly productUnit: ProductUnitInput;
  /**  default price to sell at to customer */
  readonly sellingPrice: AmountInput;
  /**  discounts applicable on the product, e.g: 10% off */
  readonly tax?: InputMaybe<ReadonlyArray<TaxInput>>;
};

export type Product = {
  readonly company: Company;
  readonly customTags?: Maybe<ReadonlyArray<CustomTag>>;
  /**  Will be used as unique identifier along with dimensions, if productCode is null */
  readonly description?: Maybe<Scalars["String"]["output"]>;
  readonly dimensions?: Maybe<ReadonlyArray<Dimension>>;
  readonly documents?: Maybe<ReadonlyArray<Document>>;
  readonly id: Scalars["ID"]["output"];
  /**  User provided, unique identifier along with dimensions, it can be null */
  readonly name: Scalars["String"]["output"];
  readonly pricing: Pricing;
  readonly productCode?: Maybe<Scalars["String"]["output"]>;
  readonly productType: ProductType;
  readonly status: ProductStatus;
  readonly tax?: Maybe<ReadonlyArray<Tax>>;
  readonly version: Scalars["ID"]["output"];
};

export enum ProductStatus {
  Active = "ACTIVE",
  Created = "CREATED",
  Inactive = "INACTIVE",
}

export enum ProductType {
  Customer = "CUSTOMER",
  Master = "MASTER",
  Quote = "QUOTE",
}

export type ProductUnit = {
  readonly __typename?: "ProductUnit";
  readonly unit: Scalars["Float"]["output"];
  /**  unit of measurement for the product, e.g: 1 */
  readonly unitType: Scalars["String"]["output"];
};

export type ProductUnitInput = {
  readonly unit: Scalars["Float"]["input"];
  /**  unit of measurement for the product, e.g: 1 */
  readonly unitType: Scalars["String"]["input"];
};

export type Query = {
  readonly __typename?: "Query";
  readonly _service: _Service;
  readonly getCompany?: Maybe<Company>;
  readonly getCompanyUser?: Maybe<CompanyUser>;
  readonly getCustomer?: Maybe<Customer>;
  readonly getCustomers: ReadonlyArray<Customer>;
  readonly masterProductGetById: MasterProduct;
  readonly masterProductsGet: ReadonlyArray<MasterProduct>;
  readonly ok?: Maybe<Scalars["Boolean"]["output"]>;
  readonly quoteGetById: Quote;
  readonly quotesGet: ReadonlyArray<Quote>;
};

export type QueryGetCustomerArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryGetCustomersArgs = {
  filters?: InputMaybe<GetCustomersQueryFilterInput>;
};

export type QueryMasterProductGetByIdArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryMasterProductsGetArgs = {
  filters?: InputMaybe<GetMasterProductFilters>;
};

export type QueryQuoteGetByIdArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryQuotesGetArgs = {
  filters?: InputMaybe<GetQuotesFilters>;
};

export type Quote = {
  readonly __typename?: "Quote";
  readonly address: QuoteAddress;
  /** calculated value from the quote product items */
  readonly assignments?: Maybe<ReadonlyArray<QuoteAssignment>>;
  readonly company: Company;
  readonly currency: Currency;
  readonly customTags?: Maybe<ReadonlyArray<CustomTag>>;
  readonly customer: Customer;
  readonly date: QuoteDate;
  readonly description?: Maybe<Scalars["String"]["output"]>;
  /** calculated value from the quote product items */
  readonly discounts: ReadonlyArray<Discount>;
  readonly documents?: Maybe<ReadonlyArray<Document>>;
  readonly id: Scalars["ID"]["output"];
  readonly products: ReadonlyArray<QuoteProduct>;
  readonly quoteTotalDiscountAmount: Amount;
  readonly quoteTotalListPrice: Amount;
  /**  quote products.listPrice */
  readonly quoteTotalSellingPrice: Amount;
  readonly quoteTotalTaxAmount: Amount;
  readonly status: QuoteStatus;
  readonly version: Scalars["ID"]["output"];
};

export type QuoteAddress = {
  readonly __typename?: "QuoteAddress";
  readonly fromAddress: Scalars["String"]["output"];
  readonly toAddress: Scalars["String"]["output"];
};

export type QuoteAddressInput = {
  readonly fromAddress: Scalars["String"]["input"];
  readonly toAddress: Scalars["String"]["input"];
};

export type QuoteAssignment = {
  readonly __typename?: "QuoteAssignment";
  readonly customerSuccessManger?: Maybe<CompanyUser>;
  readonly salesExecutive?: Maybe<CompanyUser>;
};

export type QuoteAssignmentInput = {
  readonly customerSuccessManger?: InputMaybe<Scalars["ID"]["input"]>;
  readonly salesExecutive?: InputMaybe<Scalars["ID"]["input"]>;
};

export type QuoteCustomerFilter = {
  readonly customerId: Scalars["ID"]["input"];
  readonly customerType: CustomerType;
};

export type QuoteDate = {
  readonly __typename?: "QuoteDate";
  readonly validFrom: Scalars["DateTime"]["output"];
  readonly validTill?: Maybe<Scalars["DateTime"]["output"]>;
};

export type QuoteDateInput = {
  readonly validFrom: Scalars["Date"]["input"];
  readonly validTill?: InputMaybe<Scalars["Date"]["input"]>;
};

export type QuoteProduct = Product & {
  readonly __typename?: "QuoteProduct";
  readonly company: Company;
  readonly customTags?: Maybe<ReadonlyArray<CustomTag>>;
  readonly description?: Maybe<Scalars["String"]["output"]>;
  readonly dimensions?: Maybe<ReadonlyArray<Dimension>>;
  readonly documents?: Maybe<ReadonlyArray<Document>>;
  readonly id: Scalars["ID"]["output"];
  /** User provided, can be null */
  readonly name: Scalars["String"]["output"];
  readonly pricing: Pricing;
  readonly productCode?: Maybe<Scalars["String"]["output"]>;
  readonly productType: ProductType;
  readonly quote: Quote;
  readonly referenceProduct?: Maybe<QuoteProductReferenceProduct>;
  readonly status: ProductStatus;
  readonly tax?: Maybe<ReadonlyArray<Tax>>;
  /** we don't want that, right? Shall we always keep it CREATED? */
  readonly version: Scalars["ID"]["output"];
};

export type QuoteProductCreateInput = {
  readonly customTags?: InputMaybe<ReadonlyArray<CustomTagInput>>;
  readonly customerId: Scalars["ID"]["input"];
  readonly description?: InputMaybe<Scalars["String"]["input"]>;
  readonly dimensions?: InputMaybe<ReadonlyArray<DimensionInput>>;
  readonly documents?: InputMaybe<ReadonlyArray<DocumentInput>>;
  /** non mandatory. */
  readonly name: Scalars["String"]["input"];
  readonly pricing: PricingInput;
  readonly productCode?: InputMaybe<Scalars["String"]["input"]>;
  readonly referenceProduct?: InputMaybe<QuoteProductReferenceProductInput>;
  readonly tax?: InputMaybe<ReadonlyArray<TaxInput>>;
};

export type QuoteProductReferenceProduct = {
  readonly __typename?: "QuoteProductReferenceProduct";
  readonly id?: Maybe<Scalars["ID"]["output"]>;
  readonly type?: Maybe<ProductType>;
};

export type QuoteProductReferenceProductInput = {
  readonly id: Scalars["ID"]["input"];
  readonly type: ProductType;
};

export enum QuoteStatus {
  Accepted = "ACCEPTED",
  Activated = "ACTIVATED",
  Created = "CREATED",
  Deleted = "DELETED",
  Rejected = "REJECTED",
  Sent = "SENT",
}

export type QuoteUpsertInput = {
  readonly addressInput: QuoteAddressInput;
  readonly assignments?: InputMaybe<ReadonlyArray<QuoteAssignmentInput>>;
  readonly currency: Currency;
  readonly customTags?: InputMaybe<ReadonlyArray<CustomTagInput>>;
  readonly customerId: Scalars["ID"]["input"];
  readonly dateInput: QuoteDateInput;
  readonly description?: InputMaybe<Scalars["String"]["input"]>;
  readonly discounts?: InputMaybe<ReadonlyArray<DiscountInput>>;
  readonly documents?: InputMaybe<ReadonlyArray<DocumentInput>>;
  readonly id?: InputMaybe<Scalars["ID"]["input"]>;
  readonly paymentTerms?: InputMaybe<Scalars["String"]["input"]>;
  readonly quoteProductsInput: ReadonlyArray<QuoteProductCreateInput>;
};

export type TaskResponse = {
  readonly __typename?: "TaskResponse";
  readonly message: Scalars["String"]["output"];
  readonly success: Scalars["Boolean"]["output"];
};

export type Tax = {
  readonly __typename?: "Tax";
  readonly amount: Amount;
  readonly name?: Maybe<Scalars["String"]["output"]>;
  readonly percentage: Scalars["Float"]["output"];
};

export type TaxInput = {
  readonly amount: AmountInput;
  readonly name?: InputMaybe<Scalars["String"]["input"]>;
  readonly percentage: Scalars["Float"]["input"];
};

export type _Service = {
  readonly __typename?: "_Service";
  readonly sdl: Scalars["String"]["output"];
};

export type CompanyUserCreateMutationVariables = Exact<{
  input: CompanyUserCreateInput;
}>;

export type CompanyUserCreateMutation = { readonly __typename?: "Mutation" } & {
  readonly companyUserCreate: { readonly __typename?: "CompanyUser" } & Pick<
    CompanyUser,
    "id" | "name" | "email" | "status" | "roles"
  >;
};

export type CompanyUserInviteMutationVariables = Exact<{
  companyUserId: Scalars["ID"]["input"];
}>;

export type CompanyUserInviteMutation = { readonly __typename?: "Mutation" } & {
  readonly companyUserInvite: { readonly __typename?: "CompanyUser" } & Pick<
    CompanyUser,
    "id" | "name" | "email" | "status" | "roles"
  >;
};

export type CompanyUserDeactivateMutationVariables = Exact<{
  companyUserId: Scalars["ID"]["input"];
}>;

export type CompanyUserDeactivateMutation = {
  readonly __typename?: "Mutation";
} & {
  readonly companyUserDeactivate: {
    readonly __typename?: "TaskResponse";
  } & Pick<TaskResponse, "success" | "message">;
};

export type CompanyUserActivateMutationVariables = Exact<{
  [key: string]: never;
}>;

export type CompanyUserActivateMutation = {
  readonly __typename?: "Mutation";
} & {
  readonly companyUserActivate: { readonly __typename?: "CompanyUser" } & Pick<
    CompanyUser,
    "id" | "name" | "email" | "status" | "roles"
  >;
};

export type CreateCompanyBasicDetailsMutationVariables = Exact<{
  input: CompanyCreateBasicDetailsInput;
}>;

export type CreateCompanyBasicDetailsMutation = {
  readonly __typename?: "Mutation";
} & {
  readonly companyCreateBasicDetails?: Maybe<
    { readonly __typename?: "Company" } & Pick<Company, "id"> & {
        readonly basicDetails?: Maybe<
          { readonly __typename?: "CompanyBasicDetails" } & Pick<
            CompanyBasicDetails,
            | "name"
            | "address"
            | "phoneNumber"
            | "email"
            | "website"
            | "industry"
            | "size"
          >
        >;
      }
  >;
};

export type UpdateCompanyBasicDetailsMutationVariables = Exact<{
  input: CompanyUpdateBasicDetailsInput;
}>;

export type UpdateCompanyBasicDetailsMutation = {
  readonly __typename?: "Mutation";
} & {
  readonly companyUpdateBasicDetails?: Maybe<
    { readonly __typename?: "Company" } & Pick<Company, "id"> & {
        readonly basicDetails?: Maybe<
          { readonly __typename?: "CompanyBasicDetails" } & Pick<
            CompanyBasicDetails,
            | "name"
            | "address"
            | "phoneNumber"
            | "email"
            | "website"
            | "industry"
            | "size"
          >
        >;
      }
  >;
};

export type CustomerUpsertBasicDetailsMutationVariables = Exact<{
  input: CustomerUpsertBasicDetailsInput;
}>;

export type CustomerUpsertBasicDetailsMutation = {
  readonly __typename?: "Mutation";
} & {
  readonly customerUpsertBasicDetails?: Maybe<
    | ({ readonly __typename?: "CustomerBusiness" } & Pick<
        CustomerBusiness,
        "id"
      >)
    | ({ readonly __typename?: "CustomerIndividual" } & Pick<
        CustomerIndividual,
        "id"
      >)
  >;
};

export type CustomerUpsertAdditionalDetailsMutationVariables = Exact<{
  input: CustomerUpsertAdditionalDetailsInput;
}>;

export type CustomerUpsertAdditionalDetailsMutation = {
  readonly __typename?: "Mutation";
} & {
  readonly customerUpsertAdditionalDetails?: Maybe<
    | ({ readonly __typename?: "CustomerBusiness" } & Pick<
        CustomerBusiness,
        "id"
      >)
    | ({ readonly __typename?: "CustomerIndividual" } & Pick<
        CustomerIndividual,
        "id"
      >)
  >;
};

export type MasterProductUpsertDetailsMutationVariables = Exact<{
  input: MasterProductUpsertInput;
}>;

export type MasterProductUpsertDetailsMutation = {
  readonly __typename?: "Mutation";
} & {
  readonly masterProductUpsertDetails: {
    readonly __typename?: "MasterProduct";
  } & Pick<MasterProduct, "id"> & {
      readonly pricing: { readonly __typename?: "Pricing" } & Pick<
        Pricing,
        "id"
      >;
    };
};

export type MasterProductUpsertAdditionalDetailsMutationVariables = Exact<{
  input: MasterProductUpsertAdditionalDetailsInput;
}>;

export type MasterProductUpsertAdditionalDetailsMutation = {
  readonly __typename?: "Mutation";
} & {
  readonly masterProductUpsertAdditionalDetails: {
    readonly __typename?: "MasterProduct";
  } & Pick<MasterProduct, "id"> & {
      readonly pricing: { readonly __typename?: "Pricing" } & Pick<
        Pricing,
        "id"
      >;
    };
};

export type ProposalProductUpsertMutationVariables = Exact<{
  input: QuoteUpsertInput;
}>;

export type ProposalProductUpsertMutation = {
  readonly __typename?: "Mutation";
} & {
  readonly quoteUpsert: { readonly __typename?: "Quote" } & Pick<
    Quote,
    "id" | "status"
  >;
};

export type OkQueryVariables = Exact<{ [key: string]: never }>;

export type OkQuery = { readonly __typename?: "Query" } & Pick<Query, "ok">;

export type GetCompanyQueryVariables = Exact<{ [key: string]: never }>;

export type GetCompanyQuery = { readonly __typename?: "Query" } & {
  readonly getCompany?: Maybe<
    { readonly __typename?: "Company" } & Pick<
      Company,
      "id" | "name" | "status"
    > & {
        readonly onboarding?: Maybe<
          { readonly __typename?: "CompanyOnboarding" } & Pick<
            CompanyOnboarding,
            "id"
          > & {
              readonly pendingSteps: ReadonlyArray<
                Maybe<
                  { readonly __typename?: "CompanyOnboardingStep" } & Pick<
                    CompanyOnboardingStep,
                    "stepType" | "mandatory" | "completed"
                  >
                >
              >;
              readonly completedSteps: ReadonlyArray<
                Maybe<
                  { readonly __typename?: "CompanyOnboardingStep" } & Pick<
                    CompanyOnboardingStep,
                    "stepType" | "mandatory" | "completed"
                  >
                >
              >;
            }
        >;
      }
  >;
};

export type GetCompanyBasicDetailsQueryVariables = Exact<{
  [key: string]: never;
}>;

export type GetCompanyBasicDetailsQuery = { readonly __typename?: "Query" } & {
  readonly getCompany?: Maybe<
    { readonly __typename?: "Company" } & Pick<Company, "id"> & {
        readonly basicDetails?: Maybe<
          { readonly __typename?: "CompanyBasicDetails" } & Pick<
            CompanyBasicDetails,
            | "name"
            | "address"
            | "email"
            | "website"
            | "phoneNumber"
            | "industry"
            | "size"
          >
        >;
      }
  >;
};

export type GetCompanyUsersQueryVariables = Exact<{ [key: string]: never }>;

export type GetCompanyUsersQuery = { readonly __typename?: "Query" } & {
  readonly getCompany?: Maybe<
    { readonly __typename?: "Company" } & Pick<Company, "id"> & {
        readonly users?: Maybe<
          ReadonlyArray<
            Maybe<
              { readonly __typename?: "CompanyUser" } & Pick<
                CompanyUser,
                "id" | "name" | "email" | "status" | "roles"
              >
            >
          >
        >;
      }
  >;
};

export type GetCompanyUserQueryVariables = Exact<{ [key: string]: never }>;

export type GetCompanyUserQuery = { readonly __typename?: "Query" } & {
  readonly getCompanyUser?: Maybe<
    { readonly __typename?: "CompanyUser" } & Pick<
      CompanyUser,
      "id" | "name" | "email" | "status" | "roles"
    >
  >;
};

export type GetAllCustomersQueryVariables = Exact<{
  status?: InputMaybe<CustomerStatus>;
  stage?: InputMaybe<CustomerStage>;
  customerType?: InputMaybe<CustomerType>;
}>;

export type GetAllCustomersQuery = { readonly __typename?: "Query" } & {
  readonly getCustomers: ReadonlyArray<
    | ({ readonly __typename?: "CustomerBusiness" } & Pick<
        CustomerBusiness,
        "id" | "status" | "stage" | "type"
      > & {
          readonly basicDetails: {
            readonly __typename?: "BusinessCustomerBasicDetails";
          } & Pick<
            BusinessCustomerBasicDetails,
            "legalName" | "size" | "industry" | "referralSource" | "plainTags"
          > & { businessWebsite: BusinessCustomerBasicDetails["website"] } & {
              readonly contactDetails: {
                readonly __typename?: "ContactDetails";
              } & Pick<
                ContactDetails,
                "contactType" | "name" | "title" | "email" | "phoneNo"
              >;
            };
          readonly company: { readonly __typename?: "Company" } & Pick<
            Company,
            "id"
          >;
          readonly assignments: ReadonlyArray<
            Maybe<
              { readonly __typename?: "CustomerAssignment" } & {
                readonly accountManager?: Maybe<
                  { readonly __typename?: "CompanyUser" } & Pick<
                    CompanyUser,
                    "id" | "name" | "email"
                  >
                >;
                readonly supportRepresentative?: Maybe<
                  { readonly __typename?: "CompanyUser" } & Pick<
                    CompanyUser,
                    "id" | "name" | "email"
                  >
                >;
              }
            >
          >;
        })
    | ({ readonly __typename?: "CustomerIndividual" } & Pick<
        CustomerIndividual,
        "id" | "status" | "stage" | "type"
      > & {
          readonly basicDetails: {
            readonly __typename?: "IndividualCustomerBasicDetails";
          } & Pick<
            IndividualCustomerBasicDetails,
            "plainTags" | "referralSource"
          > & {
              individualWebsite: IndividualCustomerBasicDetails["website"];
            } & {
              readonly contactDetails: {
                readonly __typename?: "ContactDetails";
              } & Pick<
                ContactDetails,
                "contactType" | "name" | "title" | "email" | "phoneNo"
              >;
            };
          readonly company: { readonly __typename?: "Company" } & Pick<
            Company,
            "id"
          >;
          readonly assignments: ReadonlyArray<
            Maybe<
              { readonly __typename?: "CustomerAssignment" } & {
                readonly accountManager?: Maybe<
                  { readonly __typename?: "CompanyUser" } & Pick<
                    CompanyUser,
                    "id" | "name" | "email"
                  >
                >;
                readonly supportRepresentative?: Maybe<
                  { readonly __typename?: "CompanyUser" } & Pick<
                    CompanyUser,
                    "id" | "name" | "email"
                  >
                >;
              }
            >
          >;
        })
  >;
};

export type GetCustomerByIdQueryVariables = Exact<{
  id: Scalars["ID"]["input"];
}>;

export type GetCustomerByIdQuery = { readonly __typename?: "Query" } & {
  readonly getCustomer?: Maybe<
    | ({ readonly __typename?: "CustomerBusiness" } & Pick<
        CustomerBusiness,
        "id" | "status" | "stage" | "type"
      > & {
          readonly basicDetails: {
            readonly __typename?: "BusinessCustomerBasicDetails";
          } & Pick<
            BusinessCustomerBasicDetails,
            "legalName" | "size" | "industry" | "referralSource" | "plainTags"
          > & { businessWebsite: BusinessCustomerBasicDetails["website"] } & {
              readonly contactDetails: {
                readonly __typename?: "ContactDetails";
              } & Pick<
                ContactDetails,
                "contactType" | "name" | "title" | "email" | "phoneNo"
              >;
            };
          readonly notes?: Maybe<
            ReadonlyArray<
              Maybe<
                { readonly __typename?: "Notes" } & Pick<
                  Notes,
                  "id" | "content" | "tags"
                >
              >
            >
          >;
          readonly assignments: ReadonlyArray<
            Maybe<
              { readonly __typename?: "CustomerAssignment" } & {
                readonly accountManager?: Maybe<
                  { readonly __typename?: "CompanyUser" } & Pick<
                    CompanyUser,
                    "id" | "name" | "email"
                  >
                >;
                readonly supportRepresentative?: Maybe<
                  { readonly __typename?: "CompanyUser" } & Pick<
                    CompanyUser,
                    "id" | "name" | "email"
                  >
                >;
              }
            >
          >;
          readonly customTags: ReadonlyArray<
            Maybe<
              { readonly __typename?: "CustomTag" } & Pick<
                CustomTag,
                "id" | "key" | "label" | "value" | "type" | "description"
              >
            >
          >;
          readonly company: { readonly __typename?: "Company" } & Pick<
            Company,
            "id"
          >;
        })
    | ({ readonly __typename?: "CustomerIndividual" } & Pick<
        CustomerIndividual,
        "id" | "status" | "stage" | "type"
      > & {
          readonly basicDetails: {
            readonly __typename?: "IndividualCustomerBasicDetails";
          } & Pick<
            IndividualCustomerBasicDetails,
            "plainTags" | "referralSource"
          > & {
              individualWebsite: IndividualCustomerBasicDetails["website"];
            } & {
              readonly contactDetails: {
                readonly __typename?: "ContactDetails";
              } & Pick<
                ContactDetails,
                "contactType" | "name" | "title" | "email" | "phoneNo"
              >;
            };
          readonly notes?: Maybe<
            ReadonlyArray<
              Maybe<
                { readonly __typename?: "Notes" } & Pick<
                  Notes,
                  "id" | "content" | "tags"
                >
              >
            >
          >;
          readonly assignments: ReadonlyArray<
            Maybe<
              { readonly __typename?: "CustomerAssignment" } & {
                readonly accountManager?: Maybe<
                  { readonly __typename?: "CompanyUser" } & Pick<
                    CompanyUser,
                    "id" | "name" | "email"
                  >
                >;
                readonly supportRepresentative?: Maybe<
                  { readonly __typename?: "CompanyUser" } & Pick<
                    CompanyUser,
                    "id" | "name" | "email"
                  >
                >;
              }
            >
          >;
          readonly customTags: ReadonlyArray<
            Maybe<
              { readonly __typename?: "CustomTag" } & Pick<
                CustomTag,
                "id" | "key" | "label" | "value" | "type" | "description"
              >
            >
          >;
          readonly company: { readonly __typename?: "Company" } & Pick<
            Company,
            "id"
          >;
        })
  >;
};

export type GetCustomerByIdWithProposalsQueryVariables = Exact<{
  id: Scalars["ID"]["input"];
}>;

export type GetCustomerByIdWithProposalsQuery = {
  readonly __typename?: "Query";
} & {
  readonly getCustomer?: Maybe<
    | ({ readonly __typename?: "CustomerBusiness" } & Pick<
        CustomerBusiness,
        "id"
      > & {
          readonly quotes?: Maybe<
            ReadonlyArray<
              { readonly __typename?: "Quote" } & Pick<
                Quote,
                "id" | "status" | "description" | "version" | "currency"
              > & {
                  readonly assignments?: Maybe<
                    ReadonlyArray<
                      { readonly __typename?: "QuoteAssignment" } & {
                        readonly salesExecutive?: Maybe<
                          { readonly __typename?: "CompanyUser" } & Pick<
                            CompanyUser,
                            "id" | "name"
                          >
                        >;
                        readonly customerSuccessManger?: Maybe<
                          { readonly __typename?: "CompanyUser" } & Pick<
                            CompanyUser,
                            "id" | "name"
                          >
                        >;
                      }
                    >
                  >;
                  readonly date: { readonly __typename?: "QuoteDate" } & Pick<
                    QuoteDate,
                    "validFrom" | "validTill"
                  >;
                  readonly quoteTotalSellingPrice: {
                    readonly __typename?: "Amount";
                  } & Pick<Amount, "value" | "currency">;
                  readonly quoteTotalTaxAmount: {
                    readonly __typename?: "Amount";
                  } & Pick<Amount, "value" | "currency">;
                  readonly quoteTotalDiscountAmount: {
                    readonly __typename?: "Amount";
                  } & Pick<Amount, "value" | "currency">;
                }
            >
          >;
        })
    | ({ readonly __typename?: "CustomerIndividual" } & Pick<
        CustomerIndividual,
        "id"
      > & {
          readonly quotes?: Maybe<
            ReadonlyArray<
              { readonly __typename?: "Quote" } & Pick<
                Quote,
                "id" | "status" | "description" | "version" | "currency"
              > & {
                  readonly assignments?: Maybe<
                    ReadonlyArray<
                      { readonly __typename?: "QuoteAssignment" } & {
                        readonly salesExecutive?: Maybe<
                          { readonly __typename?: "CompanyUser" } & Pick<
                            CompanyUser,
                            "id" | "name"
                          >
                        >;
                        readonly customerSuccessManger?: Maybe<
                          { readonly __typename?: "CompanyUser" } & Pick<
                            CompanyUser,
                            "id" | "name"
                          >
                        >;
                      }
                    >
                  >;
                  readonly date: { readonly __typename?: "QuoteDate" } & Pick<
                    QuoteDate,
                    "validFrom" | "validTill"
                  >;
                  readonly quoteTotalSellingPrice: {
                    readonly __typename?: "Amount";
                  } & Pick<Amount, "value" | "currency">;
                  readonly quoteTotalTaxAmount: {
                    readonly __typename?: "Amount";
                  } & Pick<Amount, "value" | "currency">;
                  readonly quoteTotalDiscountAmount: {
                    readonly __typename?: "Amount";
                  } & Pick<Amount, "value" | "currency">;
                }
            >
          >;
        })
  >;
};

export type GetAllProductsQueryVariables = Exact<{
  status?: InputMaybe<ProductStatus>;
}>;

export type GetAllProductsQuery = { readonly __typename?: "Query" } & {
  readonly masterProductsGet: ReadonlyArray<
    { readonly __typename?: "MasterProduct" } & Pick<
      MasterProduct,
      "id" | "name" | "description" | "status" | "productCode"
    > & {
        readonly dimensions?: Maybe<
          ReadonlyArray<
            { readonly __typename?: "Dimension" } & Pick<
              Dimension,
              "key" | "value"
            >
          >
        >;
        readonly pricing: { readonly __typename?: "Pricing" } & Pick<
          Pricing,
          "id" | "chargePolicy"
        > & {
            readonly costPrice: { readonly __typename?: "Amount" } & Pick<
              Amount,
              "value" | "currency"
            >;
            readonly listPrice: { readonly __typename?: "Amount" } & Pick<
              Amount,
              "value" | "currency"
            >;
            readonly sellingPrice: { readonly __typename?: "Amount" } & Pick<
              Amount,
              "value" | "currency"
            >;
            readonly unit: { readonly __typename?: "ProductUnit" } & Pick<
              ProductUnit,
              "unit" | "unitType"
            >;
            readonly discount: ReadonlyArray<
              Maybe<
                { readonly __typename?: "Discount" } & Pick<
                  Discount,
                  "id" | "discountType"
                > & {
                    readonly discountValue: {
                      readonly __typename?: "DiscountValue";
                    } & Pick<DiscountValue, "percentage"> & {
                        readonly value: {
                          readonly __typename?: "Amount";
                        } & Pick<Amount, "value" | "currency">;
                      };
                  }
              >
            >;
          };
      }
  >;
};

export type GetProductByIdQueryVariables = Exact<{
  id: Scalars["ID"]["input"];
}>;

export type GetProductByIdQuery = { readonly __typename?: "Query" } & {
  readonly masterProductGetById: {
    readonly __typename?: "MasterProduct";
  } & Pick<
    MasterProduct,
    "id" | "name" | "description" | "status" | "productCode"
  > & {
      readonly dimensions?: Maybe<
        ReadonlyArray<
          { readonly __typename?: "Dimension" } & Pick<
            Dimension,
            "key" | "value"
          >
        >
      >;
      readonly documents?: Maybe<
        ReadonlyArray<
          { readonly __typename?: "Document" } & Pick<Document, "id"> & {
              readonly file?: Maybe<
                { readonly __typename?: "FileLink" } & Pick<
                  FileLink,
                  "signedReadURL" | "signedWriteURL"
                >
              >;
            }
        >
      >;
      readonly customTags?: Maybe<
        ReadonlyArray<
          { readonly __typename?: "CustomTag" } & Pick<
            CustomTag,
            "id" | "key" | "label" | "value" | "type" | "description"
          >
        >
      >;
      readonly pricing: { readonly __typename?: "Pricing" } & Pick<
        Pricing,
        "id" | "chargePolicy"
      > & {
          readonly costPrice: { readonly __typename?: "Amount" } & Pick<
            Amount,
            "value" | "currency"
          >;
          readonly listPrice: { readonly __typename?: "Amount" } & Pick<
            Amount,
            "value" | "currency"
          >;
          readonly sellingPrice: { readonly __typename?: "Amount" } & Pick<
            Amount,
            "value" | "currency"
          >;
          readonly unit: { readonly __typename?: "ProductUnit" } & Pick<
            ProductUnit,
            "unit" | "unitType"
          >;
          readonly discount: ReadonlyArray<
            Maybe<
              { readonly __typename?: "Discount" } & Pick<
                Discount,
                "id" | "discountType"
              > & {
                  readonly discountValue: {
                    readonly __typename?: "DiscountValue";
                  } & Pick<DiscountValue, "percentage"> & {
                      readonly value: { readonly __typename?: "Amount" } & Pick<
                        Amount,
                        "value" | "currency"
                      >;
                    };
                }
            >
          >;
        };
    };
};

export type GetAllProposalsQueryVariables = Exact<{
  status?: InputMaybe<QuoteStatus>;
  quoteId?: InputMaybe<Scalars["ID"]["input"]>;
}>;

export type GetAllProposalsQuery = { readonly __typename?: "Query" } & {
  readonly quotesGet: ReadonlyArray<
    { readonly __typename?: "Quote" } & Pick<
      Quote,
      "id" | "description" | "status" | "version" | "currency"
    > & {
        readonly customer:
          | ({ readonly __typename?: "CustomerBusiness" } & Pick<
              CustomerBusiness,
              "id"
            > & {
                readonly basicDetails: {
                  readonly __typename?: "BusinessCustomerBasicDetails";
                } & Pick<BusinessCustomerBasicDetails, "legalName">;
              })
          | ({ readonly __typename?: "CustomerIndividual" } & Pick<
              CustomerIndividual,
              "id"
            > & {
                readonly basicDetails: {
                  readonly __typename?: "IndividualCustomerBasicDetails";
                } & {
                  readonly contactDetails: {
                    readonly __typename?: "ContactDetails";
                  } & Pick<ContactDetails, "name">;
                };
              });
        readonly assignments?: Maybe<
          ReadonlyArray<
            { readonly __typename?: "QuoteAssignment" } & {
              readonly salesExecutive?: Maybe<
                { readonly __typename?: "CompanyUser" } & Pick<
                  CompanyUser,
                  "id" | "name"
                >
              >;
              readonly customerSuccessManger?: Maybe<
                { readonly __typename?: "CompanyUser" } & Pick<
                  CompanyUser,
                  "id" | "name"
                >
              >;
            }
          >
        >;
        readonly date: { readonly __typename?: "QuoteDate" } & Pick<
          QuoteDate,
          "validFrom" | "validTill"
        >;
        readonly quoteTotalSellingPrice: {
          readonly __typename?: "Amount";
        } & Pick<Amount, "value" | "currency">;
        readonly quoteTotalTaxAmount: { readonly __typename?: "Amount" } & Pick<
          Amount,
          "value" | "currency"
        >;
        readonly quoteTotalDiscountAmount: {
          readonly __typename?: "Amount";
        } & Pick<Amount, "value" | "currency">;
      }
  >;
};

export type GetAllProposalsWithProductsQueryVariables = Exact<{
  status?: InputMaybe<QuoteStatus>;
  quoteId?: InputMaybe<Scalars["ID"]["input"]>;
}>;

export type GetAllProposalsWithProductsQuery = {
  readonly __typename?: "Query";
} & {
  readonly quotesGet: ReadonlyArray<
    { readonly __typename?: "Quote" } & Pick<
      Quote,
      "id" | "description" | "status" | "version" | "currency"
    > & {
        readonly customer:
          | ({ readonly __typename?: "CustomerBusiness" } & Pick<
              CustomerBusiness,
              "id"
            > & {
                readonly basicDetails: {
                  readonly __typename?: "BusinessCustomerBasicDetails";
                } & Pick<BusinessCustomerBasicDetails, "legalName">;
              })
          | ({ readonly __typename?: "CustomerIndividual" } & Pick<
              CustomerIndividual,
              "id"
            > & {
                readonly basicDetails: {
                  readonly __typename?: "IndividualCustomerBasicDetails";
                } & {
                  readonly contactDetails: {
                    readonly __typename?: "ContactDetails";
                  } & Pick<ContactDetails, "name">;
                };
              });
        readonly products: ReadonlyArray<
          { readonly __typename?: "QuoteProduct" } & Pick<
            QuoteProduct,
            | "id"
            | "productType"
            | "productCode"
            | "name"
            | "description"
            | "status"
            | "version"
          > & {
              readonly dimensions?: Maybe<
                ReadonlyArray<
                  { readonly __typename?: "Dimension" } & Pick<
                    Dimension,
                    "key" | "value"
                  >
                >
              >;
              readonly pricing: { readonly __typename?: "Pricing" } & Pick<
                Pricing,
                "id" | "chargePolicy"
              > & {
                  readonly tax?: Maybe<
                    ReadonlyArray<
                      { readonly __typename?: "Tax" } & Pick<
                        Tax,
                        "name" | "percentage"
                      > & {
                          readonly amount: {
                            readonly __typename?: "Amount";
                          } & Pick<Amount, "value" | "currency">;
                        }
                    >
                  >;
                  readonly discount: ReadonlyArray<
                    Maybe<
                      { readonly __typename?: "Discount" } & Pick<
                        Discount,
                        "id" | "description" | "discountType" | "discountLevel"
                      > & {
                          readonly discountValue: {
                            readonly __typename?: "DiscountValue";
                          } & Pick<DiscountValue, "percentage"> & {
                              readonly value: {
                                readonly __typename?: "Amount";
                              } & Pick<Amount, "value" | "currency">;
                            };
                        }
                    >
                  >;
                  readonly costPrice: { readonly __typename?: "Amount" } & Pick<
                    Amount,
                    "value" | "currency"
                  >;
                  readonly listPrice: { readonly __typename?: "Amount" } & Pick<
                    Amount,
                    "value" | "currency"
                  >;
                  readonly sellingPrice: {
                    readonly __typename?: "Amount";
                  } & Pick<Amount, "value" | "currency">;
                  readonly margin: { readonly __typename?: "Margin" } & Pick<
                    Margin,
                    "percentage"
                  > & {
                      readonly absoluteAmount: {
                        readonly __typename?: "Amount";
                      } & Pick<Amount, "value" | "currency">;
                    };
                  readonly unit: { readonly __typename?: "ProductUnit" } & Pick<
                    ProductUnit,
                    "unit" | "unitType"
                  >;
                };
            }
        >;
        readonly assignments?: Maybe<
          ReadonlyArray<
            { readonly __typename?: "QuoteAssignment" } & {
              readonly salesExecutive?: Maybe<
                { readonly __typename?: "CompanyUser" } & Pick<
                  CompanyUser,
                  "id" | "name"
                >
              >;
              readonly customerSuccessManger?: Maybe<
                { readonly __typename?: "CompanyUser" } & Pick<
                  CompanyUser,
                  "id" | "name"
                >
              >;
            }
          >
        >;
        readonly date: { readonly __typename?: "QuoteDate" } & Pick<
          QuoteDate,
          "validFrom" | "validTill"
        >;
        readonly quoteTotalSellingPrice: {
          readonly __typename?: "Amount";
        } & Pick<Amount, "value" | "currency">;
        readonly quoteTotalTaxAmount: { readonly __typename?: "Amount" } & Pick<
          Amount,
          "value" | "currency"
        >;
        readonly quoteTotalDiscountAmount: {
          readonly __typename?: "Amount";
        } & Pick<Amount, "value" | "currency">;
        readonly address: { readonly __typename?: "QuoteAddress" } & Pick<
          QuoteAddress,
          "fromAddress" | "toAddress"
        >;
      }
  >;
};
